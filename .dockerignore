# 忽略构建产物
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# 忽略测试文件
*.test

# 忽略输出文件
*.out

# 忽略依赖目录
vendor/

# 忽略IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 忽略日志文件
*.log
logs/

# 忽略临时文件
tmp/
temp/

# 忽略数据文件
data/
*.db
*.sqlite
*.sqlite3

# 忽略配置文件（保留示例）
configs/config.yaml
!configs/config.example.yaml

# 忽略Git文件
.git/
.gitignore

# 忽略文档
docs/
*.md
!README.md

# 忽略脚本
scripts/

# 忽略测试覆盖率文件
coverage.out
coverage.html

# 忽略环境文件
.env
.env.local
.env.*.local

# 忽略备份文件
*.bak
*.backup

# 忽略压缩文件
*.tar.gz
*.zip
*.rar
