# 编译产物
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试文件
*.test

# 输出文件
*.out

# 依赖目录
vendor/

# Go模块缓存
go.sum

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/

# 数据库文件
data/
*.db
*.sqlite
*.sqlite3

# 配置文件（保留示例文件）
configs/config.yaml
!configs/config.example.yaml
!configs/

# 环境变量文件
.env
.env.local
.env.*.local

# 备份文件
*.bak
*.backup

# 压缩文件
*.tar.gz
*.zip
*.rar

# 运行时文件
*.pid

# 覆盖率文件
coverage.out
coverage.html

# 调试文件
debug
debug.test

# 系统文件
Thumbs.db
ehthumbs.db

# 网络文件
.nfs*

# 本地测试脚本（如果有敏感信息）
test_local.sh

# Docker相关
# .dockerignore 应该被提交

# 证书文件
*.pem
*.key
*.crt
*.p12

# 密钥文件
secrets/
*.secret

# 上传文件
uploads/
files/

# 缓存目录
cache/
.cache/

# 文档生成文件
docs/_build/
site/

# 包管理器文件
Gopkg.lock

# 性能分析文件
*.prof
*.pprof

# 内存转储文件
*.mem

# 核心转储文件
core.*

# 编辑器临时文件
.#*
\#*#
.\#*

# Vim临时文件
*.swp
*.swo
*~

# Emacs临时文件
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# 本地配置覆盖
local.yaml
local.json
