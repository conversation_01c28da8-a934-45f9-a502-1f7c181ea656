# 🍯 Honeypot Admin Panel

[![Go Version](https://img.shields.io/badge/Go-1.21+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()

**蜜罐管理平台** - 专为内网离线环境设计的分布式蜜罐节点管理系统，提供完整的蜜罐部署、监控、情报收集和分析功能。

## 📚 目录

- [✨ 核心特性](#-核心特性)
- [🛠️ 技术架构](#️-技术架构)
- [📁 项目结构](#-项目结构)
- [🌐 API接口文档](#-api接口文档)
- [🚀 快速开始](#-快速开始)
- [⚙️ 配置说明](#️-配置说明)
- [🔧 开发指南](#-开发指南)
- [🔗 系统集成](#-系统集成)
- [🛠️ 故障排除](#️-故障排除)
- [🤝 贡献指南](#-贡献指南)
- [📋 更新日志](#-更新日志)
- [📄 许可证](#-许可证)
- [🙏 致谢](#-致谢)
- [📞 联系我们](#-联系我们)

## ✨ 核心特性

### 🎯 系统管理
- **用户认证与权限管理** - JWT令牌认证，安全可靠
- **统一仪表板** - 实时系统状态监控和数据可视化
- **完全离线部署** - 无需外网连接，适合内网环境

### 🖥️ 节点管理
- **分布式节点管理** - 支持多节点集中管理
- **实时状态监控** - WebSocket实时通信
- **远程命令执行** - 安全的节点控制机制

### 📦 模板与部署
- **Docker镜像管理** - 支持多种蜜罐类型
- **一键部署** - 简化的部署流程
- **版本控制** - 模板版本管理和回滚

### 🛡️ 情报收集
- **攻击数据收集** - 与DecoyWatch系统集成
- **实时数据分析** - 攻击趋势和统计分析
- **可视化展示** - 图表和报表生成

### 🔄 实时通信
- **WebSocket支持** - 双向实时通信
- **事件推送** - 实时告警和状态更新
- **消息广播** - 多客户端同步

## 🛠️ 技术架构

### 后端技术栈
- **框架**: Go 1.21+ (Gin Web Framework)
- **数据库**: MySQL 8.0+ (GORM ORM)
- **实时通信**: WebSocket (Gorilla WebSocket)
- **认证**: JWT (golang-jwt)
- **配置管理**: Viper
- **日志**: Logrus

### 前端技术栈
- **模板引擎**: Go HTML Template
- **UI框架**: Bootstrap 5.3 (本地化)
- **图标**: Bootstrap Icons (本地化)
- **图表**: Chart.js (本地化)
- **样式**: 自定义CSS + Bootstrap

### 系统集成
- **容器化**: Docker & Docker Compose
- **情报系统**: Honeypot-DecoyWatch
- **节点通信**: Honeypot-Node
- **部署方式**: 完全离线部署

## 📁 项目结构

```
Honeypot-AdminPanel/
├── 📁 cmd/                          # 应用程序入口点
│   └── 📁 admin/                   # 管理平台主程序
│       └── 📄 main.go              # 程序入口文件
├── 📁 internal/                     # 内部包（不对外暴露）
│   ├── 📁 auth/                    # 认证与授权
│   │   ├── 📄 jwt.go               # JWT令牌处理
│   │   └── 📄 middleware.go        # 认证中间件
│   ├── 📁 config/                  # 配置管理
│   │   └── 📄 config.go            # 配置结构和加载
│   ├── 📁 database/                # 数据库连接与迁移
│   │   ├── 📄 connection.go        # 数据库连接
│   │   └── 📄 migrations.go        # 数据库迁移
│   ├── 📁 handlers/                # HTTP请求处理器
│   │   ├── 📄 auth_handler.go      # 认证相关接口
│   │   ├── 📄 dashboard_handler.go # 仪表板接口
│   │   ├── 📄 deployment_handler.go # 部署管理接口
│   │   ├── 📄 intelligence_handler.go # 情报数据接口
│   │   ├── 📄 node_handler.go      # 节点管理接口
│   │   ├── 📄 template_handler.go  # 模板管理接口
│   │   └── 📄 websocket_handler.go # WebSocket处理
│   ├── 📁 models/                  # 数据模型定义
│   │   ├── 📄 deployment.go        # 部署模型
│   │   ├── 📄 node.go              # 节点模型
│   │   ├── 📄 template.go          # 模板模型
│   │   └── 📄 user.go              # 用户模型
│   └── 📁 services/                # 业务逻辑层
│       ├── 📄 dashboard_service.go # 仪表板服务
│       ├── 📄 decoywatch_service.go # DecoyWatch集成
│       ├── 📄 deployment_service.go # 部署管理服务
│       ├── 📄 node_service.go      # 节点管理服务
│       ├── 📄 template_service.go  # 模板管理服务
│       └── 📄 user_service.go      # 用户管理服务
├── 📁 web/                         # Web资源
│   ├── 📁 static/                  # 静态资源
│   │   ├── 📁 css/                 # 样式文件
│   │   ├── 📁 js/                  # JavaScript文件
│   │   └── 📁 vendor/              # 第三方库（本地化）
│   │       ├── 📁 bootstrap/       # Bootstrap框架
│   │       ├── 📁 bootstrap-icons/ # Bootstrap图标
│   │       └── 📁 chart.js/        # Chart.js图表库
│   └── 📁 templates/               # HTML模板
│       ├── 📄 dashboard.html       # 仪表板页面
│       ├── 📄 deployments.html     # 部署管理页面
│       ├── 📄 intelligence.html    # 情报数据页面
│       ├── 📄 login.html           # 登录页面
│       ├── 📄 nodes.html           # 节点管理页面
│       └── 📄 templates.html       # 模板管理页面
├── 📁 configs/                     # 配置文件
│   └── 📄 config.yaml              # 主配置文件
├── 📁 scripts/                     # 脚本文件
│   ├── 📄 fix_cdn_links.sh         # CDN链接修复脚本
│   └── 📄 fix_navigation.sh        # 导航菜单修复脚本
├── 📁 bin/                         # 编译输出目录
├── 📄 go.mod                       # Go模块定义
├── 📄 go.sum                       # Go依赖校验
├── 📄 Dockerfile                   # Docker构建文件
├── 📄 docker-compose.yml           # Docker编排文件
└── 📄 README.md                    # 项目说明文档
```

## 🌐 API接口文档

### Web页面路由
| 路由 | 方法 | 描述 | 认证 |
|------|------|------|------|
| `/` | GET | 重定向到仪表板 | ❌ |
| `/login` | GET | 用户登录页面 | ❌ |
| `/dashboard` | GET | 系统仪表板 | ✅ |
| `/nodes` | GET | 节点管理页面 | ✅ |
| `/templates` | GET | 模板管理页面 | ✅ |
| `/deployments` | GET | 部署管理页面 | ✅ |
| `/intelligence` | GET | 情报数据页面 | ✅ |

### REST API接口

#### 🔐 认证接口
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/auth/login` | POST | 用户登录 |
| `/api/v1/auth/register` | POST | 用户注册 |

#### 🖥️ 节点管理
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/nodes` | GET | 获取节点列表 |
| `/api/v1/nodes` | POST | 注册新节点 |
| `/api/v1/nodes/:id` | GET | 获取节点详情 |
| `/api/v1/nodes/:id` | PUT | 更新节点信息 |
| `/api/v1/nodes/:id` | DELETE | 删除节点 |
| `/api/v1/nodes/:id/command` | POST | 发送节点命令 |
| `/api/v1/nodes/:id/deploy` | POST | 部署到节点 |

#### 📦 模板管理
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/templates` | GET | 获取模板列表 |
| `/api/v1/templates` | POST | 创建新模板 |
| `/api/v1/templates/:id` | GET | 获取模板详情 |
| `/api/v1/templates/:id` | PUT | 更新模板 |
| `/api/v1/templates/:id` | DELETE | 删除模板 |

#### 🚀 部署管理
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/deployments` | GET | 获取部署列表 |
| `/api/v1/deployments` | POST | 创建新部署 |
| `/api/v1/deployments/:id` | GET | 获取部署详情 |
| `/api/v1/deployments/:id` | PUT | 更新部署 |
| `/api/v1/deployments/:id` | DELETE | 删除部署 |
| `/api/v1/deployments/statistics` | GET | 获取部署统计 |

#### 🛡️ 情报数据
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/intelligence/statistics` | GET | 获取攻击统计 |
| `/api/v1/intelligence/ip-statistics` | GET | 获取IP统计 |
| `/api/v1/intelligence/attack-events` | POST | 发送攻击事件 |
| `/api/v1/intelligence/attack-events/batch` | POST | 批量发送攻击事件 |
| `/api/v1/intelligence/health` | GET | DecoyWatch健康检查 |

#### 📊 仪表板
| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/dashboard/overview` | GET | 获取系统概览数据 |

### WebSocket接口
| 接口 | 描述 | 用途 |
|------|------|------|
| `/ws/node` | 节点WebSocket连接 | 节点状态上报和命令接收 |
| `/ws/admin` | 管理员WebSocket连接 | 实时状态推送和通知 |

## 🚀 快速开始

### 环境要求
- **Go**: 1.21+
- **MySQL**: 8.0+
- **Docker**: 20.10+ (可选)
- **操作系统**: Linux/macOS/Windows

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd Honeypot-AdminPanel
```

#### 2. 安装依赖
```bash
# 下载Go依赖
go mod download

# 验证依赖
go mod verify
```

#### 3. 配置数据库
```bash
# 创建MySQL数据库
mysql -u root -p
CREATE DATABASE honeypot_admin;
CREATE USER 'honeypot'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON honeypot_admin.* TO 'honeypot'@'localhost';
FLUSH PRIVILEGES;
```

#### 4. 配置文件
```bash
# 复制配置模板
cp configs/config.example.yaml configs/config.yaml

# 编辑配置文件
vim configs/config.yaml
```

#### 5. 编译运行
```bash
# 编译项目
go build -o bin/honeypot-admin ./cmd/admin

# 运行服务
./bin/honeypot-admin
```

#### 6. 访问系统
打开浏览器访问: `http://localhost:8080`

默认登录账号:
- 用户名: `admin`
- 密码: `admin123`

### Docker部署

#### 使用Docker Compose（推荐）
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f honeypot-admin
```

#### 单独使用Docker
```bash
# 构建镜像
docker build -t honeypot-admin .

# 运行容器
docker run -d \
  --name honeypot-admin \
  -p 8080:8080 \
  -v $(pwd)/configs:/app/configs \
  honeypot-admin
```

---

## ⚙️ 配置说明

### 主配置文件 (configs/config.yaml)

> 💡 **提示**: 复制 `configs/config.example.yaml` 为 `configs/config.yaml` 并根据实际环境修改配置

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release

# 数据库配置
database:
  host: "localhost"
  port: 3306
  username: "honeypot"
  password: "your_password"
  database: "honeypot_admin"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100

# JWT配置
jwt:
  secret: "your-secret-key"
  expires_hours: 24

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  file: "logs/app.log"
  max_size: 100  # MB
  max_backups: 3
  max_age: 28    # days

# 外部服务配置
external:
  decoywatch:
    url: "http://localhost:9090"
    token: "your-decoywatch-token"
    timeout: 30  # seconds
```

### 环境变量支持

系统支持通过环境变量覆盖配置：

```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USERNAME=honeypot
export DB_PASSWORD=your_password
export DB_DATABASE=honeypot_admin
export JWT_SECRET=your-secret-key
export SERVER_PORT=8080
```

---

## 🔧 开发指南

### 代码结构说明

#### 分层架构
```
┌─────────────────┐
│   Web Layer     │  ← HTML模板 + JavaScript
├─────────────────┤
│ Handler Layer   │  ← HTTP请求处理
├─────────────────┤
│ Service Layer   │  ← 业务逻辑处理
├─────────────────┤
│  Model Layer    │  ← 数据模型定义
├─────────────────┤
│Database Layer   │  ← 数据持久化
└─────────────────┘
```

#### 开发规范
1. **命名规范**: 使用驼峰命名法，接口使用动词+名词
2. **错误处理**: 统一使用自定义错误类型
3. **日志记录**: 关键操作必须记录日志
4. **测试覆盖**: 核心业务逻辑需要单元测试
5. **文档注释**: 公开接口必须有完整注释

### 添加新功能

#### 1. 添加新的API接口
```bash
# 1. 在models中定义数据模型
vim internal/models/new_model.go

# 2. 在services中实现业务逻辑
vim internal/services/new_service.go

# 3. 在handlers中添加HTTP处理器
vim internal/handlers/new_handler.go

# 4. 在main.go中注册路由
vim cmd/admin/main.go
```

#### 2. 添加新的Web页面
```bash
# 1. 创建HTML模板
vim web/templates/new_page.html

# 2. 创建JavaScript文件
vim web/static/js/new_page.js

# 3. 添加CSS样式（如需要）
vim web/static/css/new_page.css

# 4. 在main.go中添加页面路由
vim cmd/admin/main.go
```

### 调试技巧

#### 1. 启用调试模式
```bash
# 设置环境变量
export GIN_MODE=debug

# 或在配置文件中设置
server:
  mode: "debug"
```

#### 2. 查看详细日志
```bash
# 实时查看日志
tail -f logs/app.log

# 查看数据库查询日志
# 在配置中启用GORM日志
```

#### 3. 使用开发工具
```bash
# 安装开发工具
go install github.com/cosmtrek/air@latest

# 热重载开发
air
```

---

## 🔗 系统集成

### 与DecoyWatch集成

Honeypot-AdminPanel与DecoyWatch情报收集系统无缝集成：

```yaml
# 配置DecoyWatch连接
external:
  decoywatch:
    url: "http://decoywatch-server:9090"
    token: "your-api-token"
    timeout: 30
```

**集成功能**:
- 攻击事件自动上报
- 实时攻击统计查询
- IP地理位置分析
- 攻击趋势可视化

### 与Honeypot-Node集成

通过WebSocket与蜜罐节点实时通信：

**节点功能**:
- 节点状态实时上报
- 远程命令执行
- 容器部署管理
- 日志收集传输

### 部署架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Admin Panel    │    │   DecoyWatch    │    │  Honeypot Node  │
│   (管理平台)     │◄──►│   (情报收集)     │    │   (蜜罐节点)     │
│                 │    │                 │    │                 │
│  - Web管理界面   │    │  - 攻击数据存储  │    │  - 容器运行环境  │
│  - API接口      │    │  - 数据分析     │    │  - 状态上报     │
│  - WebSocket    │    │  - 统计报表     │    │  - 命令执行     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     MySQL       │
                    │   (数据存储)     │
                    └─────────────────┘

---

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查数据库配置
mysql -u honeypot -p -h localhost

# 检查防火墙设置
sudo ufw status
```

#### 2. 端口占用问题
```bash
# 查看端口占用
netstat -tlnp | grep :8080

# 杀死占用进程
sudo kill -9 <PID>

# 或修改配置文件中的端口
```

#### 3. WebSocket连接失败
```bash
# 检查防火墙WebSocket端口
sudo ufw allow 8080

# 检查代理服务器配置
# Nginx需要支持WebSocket升级
```

#### 4. 静态资源加载失败
```bash
# 检查静态资源目录权限
ls -la web/static/

# 确保所有本地资源文件存在
ls -la web/static/vendor/
```

### 日志分析

#### 应用日志
```bash
# 查看应用日志
tail -f logs/app.log

# 按级别过滤日志
grep "ERROR" logs/app.log
grep "WARN" logs/app.log
```

#### 数据库日志
```bash
# MySQL错误日志
sudo tail -f /var/log/mysql/error.log

# 慢查询日志
sudo tail -f /var/log/mysql/slow.log
```

### 性能优化

#### 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_nodes_status ON nodes(status);
CREATE INDEX idx_deployments_created_at ON service_deployments(created_at);

-- 查看查询性能
EXPLAIN SELECT * FROM nodes WHERE status = 'online';
```

#### 应用优化
```bash
# 启用生产模式
export GIN_MODE=release

# 调整数据库连接池
# 在配置文件中增加连接数
```

## 🤝 贡献指南

### 开发流程

1. **Fork项目** - 点击右上角Fork按钮
2. **创建分支** - `git checkout -b feature/new-feature`
3. **提交代码** - `git commit -am 'Add new feature'`
4. **推送分支** - `git push origin feature/new-feature`
5. **创建PR** - 在GitHub上创建Pull Request

### 代码规范

#### Go代码规范
```bash
# 格式化代码
go fmt ./...

# 静态检查
go vet ./...

# 运行测试
go test ./...

# 代码覆盖率
go test -cover ./...
```

#### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <description>

# 示例
feat(auth): add JWT token refresh
fix(database): resolve connection pool issue
docs(readme): update installation guide
```

### 测试指南

#### 单元测试
```bash
# 运行所有测试
go test ./...

# 运行特定包测试
go test ./internal/services

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

#### 集成测试
```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
go test -tags=integration ./...
```

---

## 📋 更新日志

### v1.0.0 (2025-01-13)
- ✅ 完整的蜜罐管理平台
- ✅ 用户认证与权限管理
- ✅ 节点管理与监控
- ✅ 模板管理与部署
- ✅ 情报数据收集与分析
- ✅ 实时WebSocket通信
- ✅ 完全离线部署支持
- ✅ 统一的Web界面风格

### 计划功能
- 🔄 实时告警机制
- 🔄 攻击数据深度分析
- 🔄 多租户支持
- 🔄 API限流和安全增强
- 🔄 移动端适配

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

```
MIT License

Copyright (c) 2025 Honeypot Admin Panel

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 🙏 致谢

感谢以下开源项目的支持：

- [Gin Web Framework](https://github.com/gin-gonic/gin) - 高性能Go Web框架
- [GORM](https://github.com/go-gorm/gorm) - Go ORM库
- [Gorilla WebSocket](https://github.com/gorilla/websocket) - WebSocket实现
- [Bootstrap](https://getbootstrap.com/) - 前端UI框架
- [Chart.js](https://www.chartjs.org/) - 图表库
- [JWT-Go](https://github.com/golang-jwt/jwt) - JWT实现

---

## 📞 联系我们

- **项目主页**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **文档站点**: [Documentation]

---

<div align="center">

**🍯 让蜜罐管理变得简单高效 🍯**

Made with ❤️ by Honeypot Team

</div>