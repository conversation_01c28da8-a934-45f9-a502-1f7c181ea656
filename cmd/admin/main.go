// Package main Honeypot Admin Panel API
//
// 蜜罐管理平台API文档
//
// 这是一个用于管理蜜罐节点、模板和部署的管理平台API。
// 支持用户认证、节点管理、模板管理、部署管理等功能。
//
//	@title Honeypot Admin Panel API
//	@description 蜜罐管理平台API
//	@version 1.0.0
//	@host localhost:8080
//	@BasePath /api/v1
//	@schemes http https
//
//	@securityDefinitions.apikey BearerAuth
//	@in header
//	@name Authorization
//	@description JWT Token，格式为 "Bearer {token}"
//
//	@contact.name API Support
//	@contact.email <EMAIL>
//	@license.name MIT
//	@license.url https://opensource.org/licenses/MIT
package main

import (
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"honeypot-admin/internal/config"
	"honeypot-admin/internal/database"
	"honeypot-admin/internal/handlers"
	"honeypot-admin/internal/services"
	"honeypot-admin/internal/websocket"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	_ "honeypot-admin/docs" // 导入生成的docs包
)

// Version 版本信息
const Version = "1.0.0"

func main() {
	// 解析命令行参数
	var (
		configPath = flag.String("config", "configs/config.yaml", "配置文件路径")
		port       = flag.String("port", "8080", "服务端口")
		version    = flag.Bool("version", false, "显示版本信息")
		migrate    = flag.Bool("migrate", false, "执行数据库迁移")
	)
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("Honeypot Admin Panel v%s\n", Version)
		return
	}

	// 加载配置
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 如果指定了端口，覆盖配置
	if *port != "8080" {
		cfg.Server.Port = *port
	}

	// 初始化数据库
	dbConfig := &database.Config{
		Host:      cfg.Database.Host,
		Port:      cfg.Database.Port,
		Username:  cfg.Database.Username,
		Password:  cfg.Database.Password,
		DBName:    cfg.Database.DBName,
		Charset:   cfg.Database.Charset,
		ParseTime: cfg.Database.ParseTime,
		Loc:       cfg.Database.Loc,
	}
	if err := database.Initialize(dbConfig); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 执行数据库迁移
	if *migrate {
		if err := database.AutoMigrate(); err != nil {
			log.Fatalf("Failed to migrate database: %v", err)
		}
		fmt.Println("Database migration completed successfully")
		return
	}

	// 获取数据库实例
	db := database.GetDB()

	// 创建服务
	nodeService := services.NewNodeService()
	templateService := services.NewTemplateService(db)
	deploymentService := services.NewDeploymentService(db)
	dashboardService := services.NewDashboardService()
	authService := services.NewAuthService("your-jwt-secret-key", time.Hour*24)
	decoyWatchService := services.NewDecoyWatchService(cfg)
	attackAnalysisService := services.NewAttackAnalysisService(decoyWatchService)

	// 创建WebSocket管理器
	wsManager := websocket.NewWebSocketManager(nodeService, deploymentService)

	// 创建处理器
	nodeHandler := handlers.NewNodeHandler(nodeService)
	templateHandler := handlers.NewTemplateHandler(templateService)
	deploymentHandler := handlers.NewDeploymentHandler(deploymentService, nodeService, templateService)
	dashboardHandler := handlers.NewDashboardHandler(dashboardService)
	websocketHandler := handlers.NewWebSocketHandler(wsManager, nodeService, templateService)
	authHandler := handlers.NewAuthHandler(authService)
	intelligenceHandler := handlers.NewIntelligenceHandler(decoyWatchService, attackAnalysisService)

	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 静态文件
	router.Static("/static", "./web/static")

	// 加载HTML模板
	router.LoadHTMLGlob("web/templates/*")

	// Web页面路由
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusFound, "/login")
	})

	router.GET("/login", func(c *gin.Context) {
		c.HTML(http.StatusOK, "login.html", gin.H{
			"title": "登录 - 蜜罐管理平台",
		})
	})

	router.GET("/dashboard", func(c *gin.Context) {
		c.HTML(http.StatusOK, "dashboard.html", gin.H{
			"title": "仪表板 - 蜜罐管理平台",
		})
	})

	router.GET("/nodes", func(c *gin.Context) {
		c.HTML(http.StatusOK, "nodes.html", gin.H{
			"title": "节点管理 - 蜜罐管理平台",
		})
	})

	router.GET("/templates", func(c *gin.Context) {
		c.HTML(http.StatusOK, "templates.html", gin.H{
			"title": "模板管理 - 蜜罐管理平台",
		})
	})

	router.GET("/deployments", func(c *gin.Context) {
		c.HTML(http.StatusOK, "deployments.html", gin.H{
			"title": "部署管理 - 蜜罐管理平台",
		})
	})

	router.GET("/intelligence", func(c *gin.Context) {
		c.HTML(http.StatusOK, "intelligence.html", gin.H{
			"title": "情报数据 - 蜜罐管理平台",
		})
	})

	// API路由
	api := router.Group("/api/v1")
	{
		// 认证路由
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/register", authHandler.Register)
		}

		// 节点管理
		nodes := api.Group("/nodes")
		{
			nodes.GET("", nodeHandler.ListNodes)
			nodes.POST("", nodeHandler.RegisterNode)
			nodes.GET("/:id", nodeHandler.GetNode)
			nodes.PUT("/:id", nodeHandler.UpdateNode)
			nodes.DELETE("/:id", nodeHandler.DeleteNode)
			nodes.POST("/:id/command", websocketHandler.SendNodeCommand)
			nodes.POST("/:id/deploy", websocketHandler.SendDeployCommand)
		}

		// 模板管理
		templates := api.Group("/templates")
		{
			templates.GET("", templateHandler.ListTemplates)
			templates.POST("", templateHandler.CreateTemplate)
			templates.GET("/:id", templateHandler.GetTemplate)
			templates.PUT("/:id", templateHandler.UpdateTemplate)
			templates.DELETE("/:id", templateHandler.DeleteTemplate)
		}

		// 部署管理
		deployments := api.Group("/deployments")
		{
			deployments.GET("", deploymentHandler.ListDeployments)
			deployments.POST("", deploymentHandler.CreateDeployment)
			deployments.GET("/:id", deploymentHandler.GetDeployment)
			deployments.PUT("/:id", deploymentHandler.UpdateDeployment)
			deployments.DELETE("/:id", deploymentHandler.DeleteDeployment)
			deployments.GET("/statistics", deploymentHandler.GetDeploymentStatistics)
		}

		// 仪表板
		dashboard := api.Group("/dashboard")
		{
			dashboard.GET("/overview", dashboardHandler.GetOverview)

		}

		// 情报数据
		intelligence := api.Group("/intelligence")
		{
			intelligence.GET("/statistics", intelligenceHandler.GetAttackStatistics)
			intelligence.GET("/ip-statistics", intelligenceHandler.GetIPStatistics)
			intelligence.POST("/attack-events", intelligenceHandler.SendAttackEvent)
			intelligence.POST("/attack-events/batch", intelligenceHandler.SendAttackEventsBatch)
			intelligence.GET("/health", intelligenceHandler.CheckDecoyWatchHealth)

			// 攻击数据分析
			analysis := intelligence.Group("/analysis")
			{
				analysis.GET("/trends", intelligenceHandler.GetAttackTrendAnalysis)
				analysis.GET("/geographic", intelligenceHandler.GetIPGeographicAnalysis)
				analysis.GET("/attack-types", intelligenceHandler.GetAttackTypeAnalysis)
			}
		}

		// WebSocket状态
		ws := api.Group("/websocket")
		{
			ws.GET("/status", websocketHandler.GetConnectionStatus)
			ws.POST("/broadcast", websocketHandler.BroadcastNotification)
			ws.POST("/admin/:user_id/message", websocketHandler.SendToAdmin)
		}

	}

	// 简单的健康检查路由
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "Honeypot Admin Panel is running",
		})
	})

	// Swagger API文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// WebSocket路由
	router.GET("/ws/node", websocketHandler.HandleNodeConnection)
	router.GET("/ws/admin", websocketHandler.HandleAdminConnection)

	// 启动服务器
	addr := fmt.Sprintf(":%s", cfg.Server.Port)
	fmt.Printf("Starting Honeypot Admin Panel v%s on %s\n", Version, addr)
	fmt.Printf("Dashboard: http://localhost%s/dashboard\n", addr)

	// 优雅关闭
	srv := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("Shutting down server...")

	// 关闭WebSocket管理器
	if wsManager != nil {
		// wsManager.Stop() // 如果有Stop方法的话
	}

	fmt.Println("Server stopped")
}
