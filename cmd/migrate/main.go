package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"honeypot-admin/internal/database"
)

func main() {
	// 命令行参数
	var (
		configFile = flag.String("config", "configs/config.yaml", "配置文件路径")
		dryRun     = flag.Bool("dry-run", false, "仅显示将要执行的操作，不实际执行")
		force      = flag.Bool("force", false, "强制执行迁移，即使存在风险")
		help       = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	if *help {
		showHelp()
		return
	}

	log.Println("蜜罐管理平台数据库迁移工具")
	log.Printf("配置文件: %s", *configFile)

	if *dryRun {
		log.Println("模式: 试运行（不会实际执行迁移）")
	}

	// 初始化数据库连接
	config := database.DefaultConfig()

	// 从环境变量覆盖配置
	if host := os.Getenv("DB_HOST"); host != "" {
		config.Host = host
	}
	if port := os.Getenv("DB_PORT"); port != "" {
		fmt.Sscanf(port, "%d", &config.Port)
	}
	if user := os.Getenv("DB_USER"); user != "" {
		config.Username = user
	}
	if password := os.Getenv("DB_PASSWORD"); password != "" {
		config.Password = password
	}
	if dbname := os.Getenv("DB_NAME"); dbname != "" {
		config.DBName = dbname
	}

	log.Printf("连接数据库: %s@%s:%d/%s", config.Username, config.Host, config.Port, config.DBName)

	if *dryRun {
		log.Println("试运行模式：跳过实际数据库连接")
		showMigrationPlan()
		return
	}

	// 初始化数据库连接
	if err := database.Initialize(config); err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer database.Close()

	log.Println("数据库连接成功")

	// 执行迁移
	if err := runMigration(*force); err != nil {
		log.Fatalf("迁移失败: %v", err)
	}

	log.Println("迁移完成")
}

// runMigration 执行数据库迁移
func runMigration(force bool) error {
	log.Println("开始执行数据库迁移...")

	// 检查数据库健康状态
	if err := database.HealthCheck(); err != nil {
		return fmt.Errorf("数据库健康检查失败: %w", err)
	}

	// 执行自动迁移
	log.Println("执行表结构迁移...")
	if err := database.AutoMigrate(); err != nil {
		return fmt.Errorf("表结构迁移失败: %w", err)
	}

	// 创建默认数据
	log.Println("创建默认数据...")
	if err := database.CreateDefaultData(); err != nil {
		if !force {
			return fmt.Errorf("创建默认数据失败: %w", err)
		}
		log.Printf("警告: 创建默认数据失败，但使用强制模式继续: %v", err)
	}

	// 显示迁移结果
	showMigrationResult()

	return nil
}

// showMigrationPlan 显示迁移计划
func showMigrationPlan() {
	log.Println("迁移计划:")
	log.Println("1. 检查数据库连接")
	log.Println("2. 执行表结构迁移")
	log.Println("   - users (用户表)")
	log.Println("   - nodes (节点表)")
	log.Println("   - templates (模板表)")
	log.Println("   - service_deployments (服务部署表)")
	log.Println("3. 创建默认数据")
	log.Println("   - 默认管理员用户")
	log.Println("   - 默认蜜罐模板")
	log.Println("4. 验证迁移结果")
}

// showMigrationResult 显示迁移结果
func showMigrationResult() {
	log.Println("迁移结果:")

	// 获取连接池统计
	stats := database.GetConnectionStats()
	log.Printf("数据库连接池状态: %+v", stats)

	// 检查表是否存在
	db := database.GetDB()
	if db != nil {
		tables := []string{"users", "nodes", "templates", "service_deployments"}
		for _, table := range tables {
			var count int64
			if err := db.Table(table).Count(&count).Error; err != nil {
				log.Printf("表 %s: 检查失败 (%v)", table, err)
			} else {
				log.Printf("表 %s: 存在，记录数 %d", table, count)
			}
		}
	}
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Println("蜜罐管理平台数据库迁移工具")
	fmt.Println("")
	fmt.Println("用法:")
	fmt.Println("  go run cmd/migrate/main.go [选项]")
	fmt.Println("")
	fmt.Println("选项:")
	fmt.Println("  -config string    配置文件路径 (默认: configs/config.yaml)")
	fmt.Println("  -dry-run         仅显示将要执行的操作，不实际执行")
	fmt.Println("  -force           强制执行迁移，即使存在风险")
	fmt.Println("  -help            显示帮助信息")
	fmt.Println("")
	fmt.Println("环境变量:")
	fmt.Println("  DB_HOST          数据库主机地址")
	fmt.Println("  DB_PORT          数据库端口")
	fmt.Println("  DB_USER          数据库用户名")
	fmt.Println("  DB_PASSWORD      数据库密码")
	fmt.Println("  DB_NAME          数据库名称")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  go run cmd/migrate/main.go")
	fmt.Println("  go run cmd/migrate/main.go -dry-run")
	fmt.Println("  go run cmd/migrate/main.go -force")
	fmt.Println("  DB_HOST=localhost go run cmd/migrate/main.go")
}
