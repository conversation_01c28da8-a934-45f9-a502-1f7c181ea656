# Honeypot Admin Panel 配置文件示例
# 复制此文件为 config.yaml 并根据实际环境修改配置

# 服务器配置
server:
  host: "0.0.0.0"          # 监听地址，0.0.0.0表示监听所有网卡
  port: 8080               # 监听端口
  mode: "debug"            # 运行模式: debug, release
  read_timeout: 60         # 读取超时时间(秒)
  write_timeout: 60        # 写入超时时间(秒)

# 数据库配置
database:
  host: "localhost"        # 数据库主机地址
  port: 3306              # 数据库端口
  username: "honeypot"     # 数据库用户名
  password: "your_password" # 数据库密码
  database: "honeypot_admin" # 数据库名称
  charset: "utf8mb4"       # 字符集
  timezone: "Asia/Shanghai" # 时区
  max_idle_conns: 10       # 最大空闲连接数
  max_open_conns: 100      # 最大打开连接数
  conn_max_lifetime: 3600  # 连接最大生存时间(秒)

# JWT认证配置
jwt:
  secret: "your-secret-key-change-this-in-production" # JWT密钥，生产环境必须修改
  expires_hours: 24        # Token过期时间(小时)
  refresh_hours: 168       # 刷新Token过期时间(小时，7天)

# 日志配置
log:
  level: "info"            # 日志级别: debug, info, warn, error
  format: "json"           # 日志格式: json, text
  file: "logs/app.log"     # 日志文件路径
  max_size: 100           # 单个日志文件最大大小(MB)
  max_backups: 3          # 保留的日志文件数量
  max_age: 28             # 日志文件保留天数
  compress: true          # 是否压缩旧日志文件

# 外部服务配置
external:
  # DecoyWatch情报收集系统配置
  decoywatch:
    url: "http://localhost:9090"     # DecoyWatch服务地址
    token: "your-decoywatch-token"   # API认证Token
    timeout: 30                      # 请求超时时间(秒)
    retry_times: 3                   # 重试次数
    retry_interval: 5                # 重试间隔(秒)

# WebSocket配置
websocket:
  read_buffer_size: 1024   # 读缓冲区大小
  write_buffer_size: 1024  # 写缓冲区大小
  heartbeat_interval: 30   # 心跳间隔(秒)
  max_connections: 1000    # 最大连接数

# 安全配置
security:
  cors:
    enabled: true          # 是否启用CORS
    allowed_origins:       # 允许的源
      - "http://localhost:3000"
      - "http://127.0.0.1:3000"
    allowed_methods:       # 允许的HTTP方法
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allowed_headers:       # 允许的请求头
      - "Origin"
      - "Content-Type"
      - "Authorization"
    max_age: 86400        # 预检请求缓存时间(秒)

  rate_limit:
    enabled: true         # 是否启用限流
    requests_per_minute: 60 # 每分钟请求数限制
    burst: 10            # 突发请求数

# 文件上传配置
upload:
  max_size: 104857600     # 最大文件大小(字节，100MB)
  allowed_types:          # 允许的文件类型
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "application/zip"
    - "application/tar"
  upload_path: "uploads"  # 上传文件存储路径

# 缓存配置
cache:
  type: "memory"          # 缓存类型: memory, redis
  ttl: 3600              # 默认缓存时间(秒)
  redis:                 # Redis配置(当type为redis时使用)
    host: "localhost"
    port: 6379
    password: ""
    database: 0

# 监控配置
monitoring:
  metrics:
    enabled: true         # 是否启用指标收集
    path: "/metrics"      # 指标接口路径
  health:
    enabled: true         # 是否启用健康检查
    path: "/health"       # 健康检查接口路径

# 开发配置
development:
  auto_migrate: true      # 是否自动执行数据库迁移
  seed_data: true         # 是否插入种子数据
  debug_sql: false        # 是否打印SQL语句
  hot_reload: false       # 是否启用热重载

# 生产环境配置示例
# production:
#   server:
#     mode: "release"
#   log:
#     level: "warn"
#     format: "json"
#   security:
#     rate_limit:
#       requests_per_minute: 30
#   development:
#     auto_migrate: false
#     seed_data: false
