// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/auth/change-password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "修改当前用户的密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "修改密码",
                "parameters": [
                    {
                        "description": "密码修改信息",
                        "name": "password",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PasswordChangeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/login": {
            "post": {
                "description": "用户登录获取访问令牌",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户登录",
                "parameters": [
                    {
                        "description": "登录信息",
                        "name": "login",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserLoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserLoginResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前登录用户的详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取当前用户信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/auth/register": {
            "post": {
                "description": "注册新用户账户",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "认证"
                ],
                "summary": "用户注册",
                "parameters": [
                    {
                        "description": "注册信息",
                        "name": "register",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserRegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/attack-trends": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定时间范围内的攻击趋势数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取攻击趋势",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 30,
                        "description": "天数",
                        "name": "days",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.AttackTrendsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/export": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "导出指定类型的数据报表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "导出报表",
                "parameters": [
                    {
                        "enum": [
                            "overview",
                            "attacks",
                            "nodes",
                            "security"
                        ],
                        "type": "string",
                        "description": "报表类型",
                        "name": "type",
                        "in": "query",
                        "required": true
                    },
                    {
                        "enum": [
                            "pdf",
                            "excel",
                            "csv"
                        ],
                        "type": "string",
                        "default": "pdf",
                        "description": "导出格式",
                        "name": "format",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 30,
                        "description": "时间范围（天）",
                        "name": "days",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/health": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取详细的系统健康检查结果",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取系统健康状态",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/node-status": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取所有节点的状态统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取节点状态概览",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.NodeStatusOverview"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/overview": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统概览统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取仪表板概览",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DashboardOverview"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/performance": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取系统性能指标信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取性能指标",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.PerformanceMetrics"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/realtime": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取实时的系统状态和攻击数据",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取实时数据",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/dashboard/security-alerts": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取最新的安全告警信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "仪表板"
                ],
                "summary": "获取安全告警",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "返回数量限制",
                        "name": "limit",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SecurityAlertsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/deployments": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "分页获取服务部署列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "获取部署列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "node_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "template_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "关键词",
                        "name": "keyword",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.PaginatedResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的服务部署",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "创建部署",
                "parameters": [
                    {
                        "description": "部署信息",
                        "name": "deployment",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeploymentCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerServiceDeployment"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/deployments/statistics": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取部署统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "获取部署统计",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.DeploymentStatistics"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/deployments/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取服务部署详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "获取部署详情",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "部署ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerServiceDeployment"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新服务部署配置",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "更新部署",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "部署ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "部署信息",
                        "name": "deployment",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeploymentUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerServiceDeployment"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除服务部署",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "部署管理"
                ],
                "summary": "删除部署",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "部署ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "分页获取节点列表，支持过滤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页大小",
                        "name": "size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "online",
                            "offline",
                            "error"
                        ],
                        "type": "string",
                        "description": "节点状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "节点区域",
                        "name": "region",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keyword",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.SwaggerNode"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "注册新的蜜罐节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "注册节点",
                "parameters": [
                    {
                        "description": "节点注册信息",
                        "name": "node",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NodeRegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerNode"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取节点的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点统计信息",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取节点详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerNode"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新节点的基本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "更新节点信息",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新信息",
                        "name": "node",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NodeUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerNode"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "删除节点",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{id}/deployments": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取指定节点的服务部署列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点部署列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.SwaggerServiceDeployment"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{id}/metrics": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取节点的实时指标信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "获取节点指标",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{id}/regenerate-token": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为节点重新生成认证Token",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "重新生成节点Token",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerNode"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{id}/status": {
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "手动更新节点状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "节点管理"
                ],
                "summary": "更新节点状态",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "状态信息",
                        "name": "status",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NodeStatusUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{node_id}/command": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "向指定节点发送控制命令",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "发送节点命令",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "node_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "命令信息",
                        "name": "command",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NodeCommandRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/nodes/{node_id}/deploy": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "向指定节点发送服务部署命令",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "发送部署命令",
                "parameters": [
                    {
                        "type": "string",
                        "description": "节点ID",
                        "name": "node_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "部署命令信息",
                        "name": "command",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.DeployCommandRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/templates": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "分页获取模板列表，支持过滤",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "获取模板列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页大小",
                        "name": "size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板类型",
                        "name": "type",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "模板状态",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "搜索关键词",
                        "name": "keyword",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.SwaggerTemplate"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新的蜜罐服务模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "创建模板",
                "parameters": [
                    {
                        "description": "模板信息",
                        "name": "template",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/templates/deploy": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "将模板部署到指定节点",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "部署模板",
                "parameters": [
                    {
                        "description": "部署信息",
                        "name": "deployment",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateDeployRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerServiceDeployment"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/templates/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取模板详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "获取模板信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新模板的基本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "更新模板信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新信息",
                        "name": "template",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TemplateUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.SwaggerTemplate"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定模板",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "删除模板",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/templates/{id}/pull": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "为模板拉取对应的Docker镜像",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "模板管理"
                ],
                "summary": "拉取Docker镜像",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "模板ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "分页获取用户列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页大小",
                        "name": "size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.PaginatedResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.UserInfo"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "创建新用户账户（需要管理员权限）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/users/{id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "根据ID获取用户详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "获取用户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "更新用户的基本信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "更新用户信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "更新信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UserUpdateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/handlers.Response"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "data": {
                                            "$ref": "#/definitions/models.UserInfo"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "删除指定用户（需要管理员权限）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "用户ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/websocket/admin/{user_id}/message": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "向指定管理员发送消息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "发送消息给管理员",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户ID",
                        "name": "user_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "消息信息",
                        "name": "message",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AdminMessageRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/websocket/broadcast": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "向所有管理员广播通知消息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "广播通知",
                "parameters": [
                    {
                        "description": "通知信息",
                        "name": "notification",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.NotificationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/api/v1/websocket/status": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "获取当前WebSocket连接的统计信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "获取WebSocket连接状态",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/handlers.Response"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/handlers.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/ws/admin": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "管理员通过WebSocket连接到管理平台",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "管理员WebSocket连接",
                "responses": {}
            }
        },
        "/ws/node": {
            "get": {
                "description": "节点通过WebSocket连接到管理平台",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "WebSocket"
                ],
                "summary": "节点WebSocket连接",
                "responses": {}
            }
        }
    },
    "definitions": {
        "handlers.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string"
                },
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "handlers.PaginatedResponse": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                },
                "pagination": {
                    "$ref": "#/definitions/handlers.PaginationInfo"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "handlers.PaginationInfo": {
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer"
                },
                "size": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "total_pages": {
                    "type": "integer"
                }
            }
        },
        "handlers.Response": {
            "type": "object",
            "properties": {
                "data": {},
                "message": {
                    "type": "string"
                },
                "success": {
                    "type": "boolean"
                }
            }
        },
        "models.AdminMessageRequest": {
            "type": "object",
            "required": [
                "message"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "type": {
                    "description": "info, warning, error",
                    "type": "string"
                }
            }
        },
        "models.AttackStats": {
            "type": "object",
            "properties": {
                "blocked_attacks": {
                    "type": "integer"
                },
                "today_attacks": {
                    "type": "integer"
                },
                "total_attacks": {
                    "type": "integer"
                },
                "unique_ips": {
                    "type": "integer"
                }
            }
        },
        "models.AttackTrend": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "date": {
                    "type": "string"
                },
                "severity": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.AttackTrendsResponse": {
            "type": "object",
            "properties": {
                "daily_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.DailyAttackStat"
                    }
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AttackTrend"
                    }
                },
                "labels": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "period": {
                    "type": "string"
                },
                "top_attack_types": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.AttackTypeCount"
                    }
                },
                "top_source_ips": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SourceIPCount"
                    }
                }
            }
        },
        "models.AttackTypeCount": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.ConfigParam": {
            "type": "object",
            "properties": {
                "default": {
                    "description": "默认值"
                },
                "description": {
                    "description": "参数描述",
                    "type": "string"
                },
                "max": {
                    "description": "最大值（数字类型）",
                    "type": "integer"
                },
                "max_length": {
                    "description": "最大长度（字符串类型）",
                    "type": "integer"
                },
                "min": {
                    "description": "最小值（数字类型）",
                    "type": "integer"
                },
                "options": {
                    "description": "可选值列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "required": {
                    "description": "是否必需",
                    "type": "boolean"
                },
                "type": {
                    "description": "参数类型：string, integer, boolean",
                    "type": "string"
                }
            }
        },
        "models.ConfigSchema": {
            "type": "object",
            "additionalProperties": {
                "$ref": "#/definitions/models.ConfigParam"
            }
        },
        "models.CriticalAttackAlert": {
            "type": "object",
            "properties": {
                "attack_type": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "severity": {
                    "type": "string"
                },
                "source_ip": {
                    "type": "string"
                },
                "target_node": {
                    "type": "string"
                }
            }
        },
        "models.DailyAttackStat": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "date": {
                    "type": "string"
                }
            }
        },
        "models.DashboardOverview": {
            "type": "object",
            "properties": {
                "attack_stats": {
                    "$ref": "#/definitions/models.AttackStats"
                },
                "deployment_stats": {
                    "$ref": "#/definitions/models.DeploymentStats"
                },
                "node_stats": {
                    "$ref": "#/definitions/models.NodeStats"
                },
                "system_status": {
                    "$ref": "#/definitions/models.SystemStatus"
                },
                "template_stats": {
                    "$ref": "#/definitions/models.TemplateStats"
                }
            }
        },
        "models.DatabaseStats": {
            "type": "object",
            "properties": {
                "connections": {
                    "type": "integer"
                },
                "database_size": {
                    "type": "integer"
                },
                "index_size": {
                    "type": "integer"
                },
                "queries_per_sec": {
                    "type": "number"
                },
                "slow_queries": {
                    "type": "integer"
                },
                "table_count": {
                    "type": "integer"
                }
            }
        },
        "models.DefaultConfig": {
            "type": "object",
            "additionalProperties": true
        },
        "models.DeployCommandRequest": {
            "type": "object",
            "required": [
                "action",
                "template_id"
            ],
            "properties": {
                "action": {
                    "description": "deploy, stop, restart",
                    "type": "string"
                },
                "config": {
                    "type": "object",
                    "additionalProperties": true
                },
                "template_id": {
                    "type": "string"
                }
            }
        },
        "models.DeploymentCreateRequest": {
            "type": "object",
            "required": [
                "name",
                "node_id",
                "template_id"
            ],
            "properties": {
                "config": {
                    "type": "object",
                    "additionalProperties": true
                },
                "environment": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "node_id": {
                    "type": "string"
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "template_id": {
                    "type": "integer"
                },
                "volumes": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.DeploymentStatistics": {
            "type": "object",
            "properties": {
                "failed": {
                    "type": "integer"
                },
                "running": {
                    "type": "integer"
                },
                "stopped": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.DeploymentStats": {
            "type": "object",
            "properties": {
                "failed": {
                    "type": "integer"
                },
                "running": {
                    "type": "integer"
                },
                "stopped": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.DeploymentUpdateRequest": {
            "type": "object",
            "properties": {
                "config": {
                    "type": "object",
                    "additionalProperties": true
                },
                "environment": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "name": {
                    "type": "string",
                    "maxLength": 100
                },
                "ports": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "volumes": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.NetworkStats": {
            "type": "object",
            "properties": {
                "bandwidth": {
                    "type": "number"
                },
                "bytes_in": {
                    "type": "integer"
                },
                "bytes_out": {
                    "type": "integer"
                },
                "connections": {
                    "type": "integer"
                },
                "packets_in": {
                    "type": "integer"
                },
                "packets_out": {
                    "type": "integer"
                }
            }
        },
        "models.NodeCommandRequest": {
            "type": "object",
            "required": [
                "command"
            ],
            "properties": {
                "command": {
                    "type": "string"
                },
                "params": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "models.NodeRegionStat": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "region": {
                    "type": "string"
                }
            }
        },
        "models.NodeRegisterRequest": {
            "type": "object",
            "required": [
                "id",
                "ip",
                "name"
            ],
            "properties": {
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "region": {
                    "type": "string"
                }
            }
        },
        "models.NodeStats": {
            "type": "object",
            "properties": {
                "critical": {
                    "type": "integer"
                },
                "error": {
                    "type": "integer"
                },
                "offline": {
                    "type": "integer"
                },
                "online": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                },
                "warning": {
                    "type": "integer"
                }
            }
        },
        "models.NodeStatusOverview": {
            "type": "object",
            "properties": {
                "attack_count": {
                    "type": "integer"
                },
                "cpu_usage": {
                    "type": "number"
                },
                "last_seen": {
                    "type": "string"
                },
                "memory_usage": {
                    "type": "number"
                },
                "node_id": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "recent_active_nodes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.RecentActiveNode"
                    }
                },
                "region": {
                    "type": "string"
                },
                "region_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.NodeRegionStat"
                    }
                },
                "status": {
                    "type": "string"
                },
                "status_stats": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.NodeStatusStat"
                    }
                }
            }
        },
        "models.NodeStatusStat": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "models.NodeStatusUpdateRequest": {
            "type": "object",
            "required": [
                "status"
            ],
            "properties": {
                "status": {
                    "type": "string",
                    "enum": [
                        "online",
                        "offline",
                        "maintenance",
                        "error"
                    ]
                }
            }
        },
        "models.NodeUpdateRequest": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "maxLength": 500
                },
                "ip": {
                    "type": "string"
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                },
                "region": {
                    "type": "string",
                    "maxLength": 50
                }
            }
        },
        "models.NotificationRequest": {
            "type": "object",
            "required": [
                "message",
                "title"
            ],
            "properties": {
                "message": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "type": {
                    "description": "info, warning, error, success",
                    "type": "string"
                }
            }
        },
        "models.OfflineNodeAlert": {
            "type": "object",
            "properties": {
                "id": {
                    "type": "integer"
                },
                "last_seen": {
                    "type": "string"
                },
                "node_id": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "offline_for": {
                    "type": "string"
                }
            }
        },
        "models.PasswordChangeRequest": {
            "type": "object",
            "required": [
                "new_password",
                "old_password"
            ],
            "properties": {
                "new_password": {
                    "type": "string",
                    "minLength": 6
                },
                "old_password": {
                    "type": "string"
                }
            }
        },
        "models.PerformanceMetrics": {
            "type": "object",
            "properties": {
                "database_stats": {
                    "$ref": "#/definitions/models.DatabaseStats"
                },
                "network_stats": {
                    "$ref": "#/definitions/models.NetworkStats"
                },
                "processing_stats": {
                    "$ref": "#/definitions/models.ProcessingStats"
                },
                "storage_stats": {
                    "$ref": "#/definitions/models.StorageStats"
                }
            }
        },
        "models.ProcessingStats": {
            "type": "object",
            "properties": {
                "attacks_last_24_hours": {
                    "type": "integer"
                },
                "attacks_last_hour": {
                    "type": "integer"
                },
                "attacks_per_hour": {
                    "type": "number"
                },
                "attacks_per_minute": {
                    "type": "number"
                },
                "error_rate": {
                    "type": "number"
                },
                "events_per_sec": {
                    "type": "number"
                },
                "processing_delay": {
                    "type": "number"
                },
                "queue_size": {
                    "type": "integer"
                },
                "throughput": {
                    "type": "number"
                }
            }
        },
        "models.RecentActiveNode": {
            "type": "object",
            "properties": {
                "last_seen": {
                    "type": "string"
                },
                "node_id": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                }
            }
        },
        "models.ResourceRequirements": {
            "type": "object",
            "properties": {
                "cpu": {
                    "description": "CPU需求，如：\"100m\"",
                    "type": "string"
                },
                "memory": {
                    "description": "内存需求，如：\"128Mi\"",
                    "type": "string"
                }
            }
        },
        "models.SecurityAlert": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "description": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "node_id": {
                    "type": "string"
                },
                "node_name": {
                    "type": "string"
                },
                "severity": {
                    "type": "string"
                },
                "source_ip": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "title": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                }
            }
        },
        "models.SecurityAlertsResponse": {
            "type": "object",
            "properties": {
                "alerts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SecurityAlert"
                    }
                },
                "critical_attacks": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.CriticalAttackAlert"
                    }
                },
                "generated_at": {
                    "type": "string"
                },
                "offline_nodes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.OfflineNodeAlert"
                    }
                },
                "suspicious_ips": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SuspiciousIPAlert"
                    }
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.SourceIPCount": {
            "type": "object",
            "properties": {
                "count": {
                    "type": "integer"
                },
                "ip": {
                    "type": "string"
                }
            }
        },
        "models.StorageStats": {
            "type": "object",
            "properties": {
                "backup_size": {
                    "type": "integer"
                },
                "data_size": {
                    "type": "integer"
                },
                "free_space": {
                    "type": "integer"
                },
                "index_size": {
                    "type": "integer"
                },
                "log_size": {
                    "type": "integer"
                },
                "total_size": {
                    "type": "integer"
                },
                "total_space": {
                    "type": "integer"
                },
                "usage_percent": {
                    "type": "number"
                },
                "used_space": {
                    "type": "integer"
                }
            }
        },
        "models.SuspiciousIPAlert": {
            "type": "object",
            "properties": {
                "attack_count": {
                    "type": "integer"
                },
                "country": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "ip": {
                    "type": "string"
                },
                "last_seen": {
                    "type": "string"
                },
                "reason": {
                    "type": "string"
                }
            }
        },
        "models.SwaggerDeletedAt": {
            "type": "object",
            "properties": {
                "time": {
                    "type": "string"
                },
                "valid": {
                    "type": "boolean"
                }
            }
        },
        "models.SwaggerNode": {
            "type": "object",
            "properties": {
                "arch": {
                    "type": "string"
                },
                "cpu_cores": {
                    "type": "integer"
                },
                "created_at": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/models.SwaggerDeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "disk": {
                    "type": "integer"
                },
                "id": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "last_connected_at": {
                    "type": "string"
                },
                "last_heartbeat_at": {
                    "type": "string"
                },
                "memory": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "os": {
                    "type": "string"
                },
                "port": {
                    "type": "integer"
                },
                "region": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "models.SwaggerServiceDeployment": {
            "type": "object",
            "properties": {
                "config": {
                    "type": "string"
                },
                "container_id": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/models.SwaggerDeletedAt"
                },
                "environment": {
                    "type": "string"
                },
                "error": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "image_name": {
                    "type": "string"
                },
                "logs": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "node_id": {
                    "type": "string"
                },
                "ports": {
                    "type": "string"
                },
                "started_at": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "stopped_at": {
                    "type": "string"
                },
                "template_id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "volumes": {
                    "type": "string"
                }
            }
        },
        "models.SwaggerTemplate": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "author": {
                    "type": "string"
                },
                "config_schema": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string"
                },
                "created_by": {
                    "type": "string"
                },
                "default_config": {
                    "type": "string"
                },
                "deleted_at": {
                    "$ref": "#/definitions/models.SwaggerDeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "documentation": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "image_name": {
                    "type": "string"
                },
                "image_tag": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "resource_requirements": {
                    "type": "string"
                },
                "status": {
                    "type": "string"
                },
                "tags": {
                    "type": "string"
                },
                "type": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "models.SystemStatus": {
            "type": "object",
            "properties": {
                "cpu_usage": {
                    "type": "number"
                },
                "disk_usage": {
                    "type": "number"
                },
                "issues": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "memory_usage": {
                    "type": "number"
                },
                "status": {
                    "type": "string"
                },
                "updated_at": {
                    "type": "string"
                },
                "uptime": {
                    "type": "string"
                },
                "version": {
                    "type": "string"
                }
            }
        },
        "models.TemplateCreateRequest": {
            "type": "object",
            "required": [
                "image_name",
                "name",
                "type"
            ],
            "properties": {
                "config_schema": {
                    "$ref": "#/definitions/models.ConfigSchema"
                },
                "default_config": {
                    "$ref": "#/definitions/models.DefaultConfig"
                },
                "description": {
                    "type": "string",
                    "maxLength": 1000
                },
                "image_name": {
                    "type": "string",
                    "maxLength": 255
                },
                "image_tag": {
                    "type": "string",
                    "maxLength": 50
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                },
                "resource_requirements": {
                    "$ref": "#/definitions/models.ResourceRequirements"
                },
                "type": {
                    "enum": [
                        "ssh",
                        "web",
                        "ftp",
                        "telnet",
                        "custom"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.TemplateType"
                        }
                    ]
                },
                "version": {
                    "type": "string",
                    "maxLength": 20
                }
            }
        },
        "models.TemplateDeployRequest": {
            "type": "object",
            "required": [
                "config",
                "node_ids"
            ],
            "properties": {
                "config": {
                    "type": "object",
                    "additionalProperties": true
                },
                "node_ids": {
                    "type": "array",
                    "minItems": 1,
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.TemplateStats": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "integer"
                },
                "deployed": {
                    "type": "integer"
                },
                "inactive": {
                    "type": "integer"
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "models.TemplateStatus": {
            "type": "string",
            "enum": [
                "active",
                "inactive",
                "processing"
            ],
            "x-enum-comments": {
                "TemplateStatusActive": "活跃",
                "TemplateStatusInactive": "非活跃",
                "TemplateStatusProcessing": "处理中"
            },
            "x-enum-varnames": [
                "TemplateStatusActive",
                "TemplateStatusInactive",
                "TemplateStatusProcessing"
            ]
        },
        "models.TemplateType": {
            "type": "string",
            "enum": [
                "ssh",
                "web",
                "ftp",
                "telnet",
                "custom"
            ],
            "x-enum-comments": {
                "TemplateTypeCustom": "自定义蜜罐",
                "TemplateTypeFTP": "FTP蜜罐",
                "TemplateTypeSSH": "SSH蜜罐",
                "TemplateTypeTelnet": "Telnet蜜罐",
                "TemplateTypeWeb": "Web蜜罐"
            },
            "x-enum-varnames": [
                "TemplateTypeSSH",
                "TemplateTypeWeb",
                "TemplateTypeFTP",
                "TemplateTypeTelnet",
                "TemplateTypeCustom"
            ]
        },
        "models.TemplateUpdateRequest": {
            "type": "object",
            "properties": {
                "config_schema": {
                    "$ref": "#/definitions/models.ConfigSchema"
                },
                "default_config": {
                    "$ref": "#/definitions/models.DefaultConfig"
                },
                "description": {
                    "type": "string",
                    "maxLength": 1000
                },
                "name": {
                    "type": "string",
                    "maxLength": 100,
                    "minLength": 1
                },
                "resource_requirements": {
                    "$ref": "#/definitions/models.ResourceRequirements"
                },
                "status": {
                    "enum": [
                        "active",
                        "inactive"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.TemplateStatus"
                        }
                    ]
                }
            }
        },
        "models.UserCreateRequest": {
            "type": "object",
            "required": [
                "password",
                "role",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "role": {
                    "enum": [
                        "administrator",
                        "operator",
                        "observer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.UserRole"
                        }
                    ]
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "models.UserInfo": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "role": {
                    "$ref": "#/definitions/models.UserRole"
                },
                "role_name": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.UserLoginRequest": {
            "type": "object",
            "required": [
                "password",
                "username"
            ],
            "properties": {
                "password": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "models.UserLoginResponse": {
            "type": "object",
            "properties": {
                "expires_in": {
                    "type": "integer"
                },
                "token": {
                    "type": "string"
                },
                "user_info": {
                    "$ref": "#/definitions/models.UserInfo"
                }
            }
        },
        "models.UserRegisterRequest": {
            "type": "object",
            "required": [
                "email",
                "password",
                "username"
            ],
            "properties": {
                "email": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "role": {
                    "type": "string",
                    "enum": [
                        "administrator",
                        "operator",
                        "observer"
                    ]
                },
                "username": {
                    "type": "string",
                    "maxLength": 50,
                    "minLength": 3
                }
            }
        },
        "models.UserRole": {
            "type": "string",
            "enum": [
                "administrator",
                "operator",
                "observer"
            ],
            "x-enum-comments": {
                "RoleAdministrator": "管理员 - 全部功能权限",
                "RoleObserver": "观察者 - 仅查看权限",
                "RoleOperator": "操作员 - 节点管理权限"
            },
            "x-enum-varnames": [
                "RoleAdministrator",
                "RoleOperator",
                "RoleObserver"
            ]
        },
        "models.UserStatus": {
            "type": "string",
            "enum": [
                "active",
                "inactive"
            ],
            "x-enum-comments": {
                "StatusActive": "活跃",
                "StatusInactive": "非活跃"
            },
            "x-enum-varnames": [
                "StatusActive",
                "StatusInactive"
            ]
        },
        "models.UserUpdateRequest": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "role": {
                    "enum": [
                        "administrator",
                        "operator",
                        "observer"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.UserRole"
                        }
                    ]
                },
                "status": {
                    "enum": [
                        "active",
                        "inactive"
                    ],
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.UserStatus"
                        }
                    ]
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "JWT Token，格式为 \"Bearer {token}\"",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{"http", "https"},
	Title:            "Honeypot Admin Panel API",
	Description:      "蜜罐管理平台API",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
