basePath: /api/v1
definitions:
  handlers.ErrorResponse:
    properties:
      error:
        type: string
      message:
        type: string
      success:
        type: boolean
    type: object
  handlers.PaginatedResponse:
    properties:
      data: {}
      message:
        type: string
      pagination:
        $ref: '#/definitions/handlers.PaginationInfo'
      success:
        type: boolean
    type: object
  handlers.PaginationInfo:
    properties:
      page:
        type: integer
      size:
        type: integer
      total:
        type: integer
      total_pages:
        type: integer
    type: object
  handlers.Response:
    properties:
      data: {}
      message:
        type: string
      success:
        type: boolean
    type: object
  models.AdminMessageRequest:
    properties:
      message:
        type: string
      type:
        description: info, warning, error
        type: string
    required:
    - message
    type: object
  models.AttackStats:
    properties:
      blocked_attacks:
        type: integer
      today_attacks:
        type: integer
      total_attacks:
        type: integer
      unique_ips:
        type: integer
    type: object
  models.AttackTrend:
    properties:
      count:
        type: integer
      date:
        type: string
      severity:
        type: string
      type:
        type: string
    type: object
  models.AttackTrendsResponse:
    properties:
      daily_stats:
        items:
          $ref: '#/definitions/models.DailyAttackStat'
        type: array
      data:
        items:
          $ref: '#/definitions/models.AttackTrend'
        type: array
      labels:
        items:
          type: string
        type: array
      period:
        type: string
      top_attack_types:
        items:
          $ref: '#/definitions/models.AttackTypeCount'
        type: array
      top_source_ips:
        items:
          $ref: '#/definitions/models.SourceIPCount'
        type: array
    type: object
  models.AttackTypeCount:
    properties:
      count:
        type: integer
      type:
        type: string
    type: object
  models.ConfigParam:
    properties:
      default:
        description: 默认值
      description:
        description: 参数描述
        type: string
      max:
        description: 最大值（数字类型）
        type: integer
      max_length:
        description: 最大长度（字符串类型）
        type: integer
      min:
        description: 最小值（数字类型）
        type: integer
      options:
        description: 可选值列表
        items:
          type: string
        type: array
      required:
        description: 是否必需
        type: boolean
      type:
        description: 参数类型：string, integer, boolean
        type: string
    type: object
  models.ConfigSchema:
    additionalProperties:
      $ref: '#/definitions/models.ConfigParam'
    type: object
  models.CriticalAttackAlert:
    properties:
      attack_type:
        type: string
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      severity:
        type: string
      source_ip:
        type: string
      target_node:
        type: string
    type: object
  models.DailyAttackStat:
    properties:
      count:
        type: integer
      date:
        type: string
    type: object
  models.DashboardOverview:
    properties:
      attack_stats:
        $ref: '#/definitions/models.AttackStats'
      deployment_stats:
        $ref: '#/definitions/models.DeploymentStats'
      node_stats:
        $ref: '#/definitions/models.NodeStats'
      system_status:
        $ref: '#/definitions/models.SystemStatus'
      template_stats:
        $ref: '#/definitions/models.TemplateStats'
    type: object
  models.DatabaseStats:
    properties:
      connections:
        type: integer
      database_size:
        type: integer
      index_size:
        type: integer
      queries_per_sec:
        type: number
      slow_queries:
        type: integer
      table_count:
        type: integer
    type: object
  models.DefaultConfig:
    additionalProperties: true
    type: object
  models.DeployCommandRequest:
    properties:
      action:
        description: deploy, stop, restart
        type: string
      config:
        additionalProperties: true
        type: object
      template_id:
        type: string
    required:
    - action
    - template_id
    type: object
  models.DeploymentCreateRequest:
    properties:
      config:
        additionalProperties: true
        type: object
      environment:
        additionalProperties:
          type: string
        type: object
      name:
        maxLength: 100
        type: string
      node_id:
        type: string
      ports:
        items:
          type: string
        type: array
      template_id:
        type: integer
      volumes:
        items:
          type: string
        type: array
    required:
    - name
    - node_id
    - template_id
    type: object
  models.DeploymentStatistics:
    properties:
      failed:
        type: integer
      running:
        type: integer
      stopped:
        type: integer
      total:
        type: integer
    type: object
  models.DeploymentStats:
    properties:
      failed:
        type: integer
      running:
        type: integer
      stopped:
        type: integer
      total:
        type: integer
    type: object
  models.DeploymentUpdateRequest:
    properties:
      config:
        additionalProperties: true
        type: object
      environment:
        additionalProperties:
          type: string
        type: object
      name:
        maxLength: 100
        type: string
      ports:
        items:
          type: string
        type: array
      volumes:
        items:
          type: string
        type: array
    type: object
  models.NetworkStats:
    properties:
      bandwidth:
        type: number
      bytes_in:
        type: integer
      bytes_out:
        type: integer
      connections:
        type: integer
      packets_in:
        type: integer
      packets_out:
        type: integer
    type: object
  models.NodeCommandRequest:
    properties:
      command:
        type: string
      params:
        additionalProperties: true
        type: object
    required:
    - command
    type: object
  models.NodeRegionStat:
    properties:
      count:
        type: integer
      region:
        type: string
    type: object
  models.NodeRegisterRequest:
    properties:
      description:
        type: string
      id:
        type: string
      ip:
        type: string
      name:
        type: string
      region:
        type: string
    required:
    - id
    - ip
    - name
    type: object
  models.NodeStats:
    properties:
      critical:
        type: integer
      error:
        type: integer
      offline:
        type: integer
      online:
        type: integer
      total:
        type: integer
      warning:
        type: integer
    type: object
  models.NodeStatusOverview:
    properties:
      attack_count:
        type: integer
      cpu_usage:
        type: number
      last_seen:
        type: string
      memory_usage:
        type: number
      node_id:
        type: string
      node_name:
        type: string
      recent_active_nodes:
        items:
          $ref: '#/definitions/models.RecentActiveNode'
        type: array
      region:
        type: string
      region_stats:
        items:
          $ref: '#/definitions/models.NodeRegionStat'
        type: array
      status:
        type: string
      status_stats:
        items:
          $ref: '#/definitions/models.NodeStatusStat'
        type: array
    type: object
  models.NodeStatusStat:
    properties:
      count:
        type: integer
      status:
        type: string
    type: object
  models.NodeStatusUpdateRequest:
    properties:
      status:
        enum:
        - online
        - offline
        - maintenance
        - error
        type: string
    required:
    - status
    type: object
  models.NodeUpdateRequest:
    properties:
      description:
        maxLength: 500
        type: string
      ip:
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
      region:
        maxLength: 50
        type: string
    type: object
  models.NotificationRequest:
    properties:
      message:
        type: string
      title:
        type: string
      type:
        description: info, warning, error, success
        type: string
    required:
    - message
    - title
    type: object
  models.OfflineNodeAlert:
    properties:
      id:
        type: integer
      last_seen:
        type: string
      node_id:
        type: string
      node_name:
        type: string
      offline_for:
        type: string
    type: object
  models.PasswordChangeRequest:
    properties:
      new_password:
        minLength: 6
        type: string
      old_password:
        type: string
    required:
    - new_password
    - old_password
    type: object
  models.PerformanceMetrics:
    properties:
      database_stats:
        $ref: '#/definitions/models.DatabaseStats'
      network_stats:
        $ref: '#/definitions/models.NetworkStats'
      processing_stats:
        $ref: '#/definitions/models.ProcessingStats'
      storage_stats:
        $ref: '#/definitions/models.StorageStats'
    type: object
  models.ProcessingStats:
    properties:
      attacks_last_24_hours:
        type: integer
      attacks_last_hour:
        type: integer
      attacks_per_hour:
        type: number
      attacks_per_minute:
        type: number
      error_rate:
        type: number
      events_per_sec:
        type: number
      processing_delay:
        type: number
      queue_size:
        type: integer
      throughput:
        type: number
    type: object
  models.RecentActiveNode:
    properties:
      last_seen:
        type: string
      node_id:
        type: string
      node_name:
        type: string
      status:
        type: string
    type: object
  models.ResourceRequirements:
    properties:
      cpu:
        description: CPU需求，如："100m"
        type: string
      memory:
        description: 内存需求，如："128Mi"
        type: string
    type: object
  models.SecurityAlert:
    properties:
      created_at:
        type: string
      description:
        type: string
      id:
        type: integer
      node_id:
        type: string
      node_name:
        type: string
      severity:
        type: string
      source_ip:
        type: string
      status:
        type: string
      title:
        type: string
      type:
        type: string
    type: object
  models.SecurityAlertsResponse:
    properties:
      alerts:
        items:
          $ref: '#/definitions/models.SecurityAlert'
        type: array
      critical_attacks:
        items:
          $ref: '#/definitions/models.CriticalAttackAlert'
        type: array
      generated_at:
        type: string
      offline_nodes:
        items:
          $ref: '#/definitions/models.OfflineNodeAlert'
        type: array
      suspicious_ips:
        items:
          $ref: '#/definitions/models.SuspiciousIPAlert'
        type: array
      total:
        type: integer
    type: object
  models.SourceIPCount:
    properties:
      count:
        type: integer
      ip:
        type: string
    type: object
  models.StorageStats:
    properties:
      backup_size:
        type: integer
      data_size:
        type: integer
      free_space:
        type: integer
      index_size:
        type: integer
      log_size:
        type: integer
      total_size:
        type: integer
      total_space:
        type: integer
      usage_percent:
        type: number
      used_space:
        type: integer
    type: object
  models.SuspiciousIPAlert:
    properties:
      attack_count:
        type: integer
      country:
        type: string
      id:
        type: integer
      ip:
        type: string
      last_seen:
        type: string
      reason:
        type: string
    type: object
  models.SwaggerDeletedAt:
    properties:
      time:
        type: string
      valid:
        type: boolean
    type: object
  models.SwaggerNode:
    properties:
      arch:
        type: string
      cpu_cores:
        type: integer
      created_at:
        type: string
      deleted_at:
        $ref: '#/definitions/models.SwaggerDeletedAt'
      description:
        type: string
      disk:
        type: integer
      id:
        type: string
      ip:
        type: string
      last_connected_at:
        type: string
      last_heartbeat_at:
        type: string
      memory:
        type: integer
      name:
        type: string
      os:
        type: string
      port:
        type: integer
      region:
        type: string
      status:
        type: string
      tags:
        type: string
      updated_at:
        type: string
      version:
        type: string
    type: object
  models.SwaggerServiceDeployment:
    properties:
      config:
        type: string
      container_id:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      deleted_at:
        $ref: '#/definitions/models.SwaggerDeletedAt'
      environment:
        type: string
      error:
        type: string
      id:
        type: integer
      image_name:
        type: string
      logs:
        type: string
      name:
        type: string
      node_id:
        type: string
      ports:
        type: string
      started_at:
        type: string
      status:
        type: string
      stopped_at:
        type: string
      template_id:
        type: integer
      updated_at:
        type: string
      volumes:
        type: string
    type: object
  models.SwaggerTemplate:
    properties:
      active:
        type: boolean
      author:
        type: string
      config_schema:
        type: string
      created_at:
        type: string
      created_by:
        type: string
      default_config:
        type: string
      deleted_at:
        $ref: '#/definitions/models.SwaggerDeletedAt'
      description:
        type: string
      documentation:
        type: string
      id:
        type: integer
      image_name:
        type: string
      image_tag:
        type: string
      name:
        type: string
      resource_requirements:
        type: string
      status:
        type: string
      tags:
        type: string
      type:
        type: string
      updated_at:
        type: string
      version:
        type: string
    type: object
  models.SystemStatus:
    properties:
      cpu_usage:
        type: number
      disk_usage:
        type: number
      issues:
        items:
          type: string
        type: array
      memory_usage:
        type: number
      status:
        type: string
      updated_at:
        type: string
      uptime:
        type: string
      version:
        type: string
    type: object
  models.TemplateCreateRequest:
    properties:
      config_schema:
        $ref: '#/definitions/models.ConfigSchema'
      default_config:
        $ref: '#/definitions/models.DefaultConfig'
      description:
        maxLength: 1000
        type: string
      image_name:
        maxLength: 255
        type: string
      image_tag:
        maxLength: 50
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
      resource_requirements:
        $ref: '#/definitions/models.ResourceRequirements'
      type:
        allOf:
        - $ref: '#/definitions/models.TemplateType'
        enum:
        - ssh
        - web
        - ftp
        - telnet
        - custom
      version:
        maxLength: 20
        type: string
    required:
    - image_name
    - name
    - type
    type: object
  models.TemplateDeployRequest:
    properties:
      config:
        additionalProperties: true
        type: object
      node_ids:
        items:
          type: string
        minItems: 1
        type: array
    required:
    - config
    - node_ids
    type: object
  models.TemplateStats:
    properties:
      active:
        type: integer
      deployed:
        type: integer
      inactive:
        type: integer
      total:
        type: integer
    type: object
  models.TemplateStatus:
    enum:
    - active
    - inactive
    - processing
    type: string
    x-enum-comments:
      TemplateStatusActive: 活跃
      TemplateStatusInactive: 非活跃
      TemplateStatusProcessing: 处理中
    x-enum-varnames:
    - TemplateStatusActive
    - TemplateStatusInactive
    - TemplateStatusProcessing
  models.TemplateType:
    enum:
    - ssh
    - web
    - ftp
    - telnet
    - custom
    type: string
    x-enum-comments:
      TemplateTypeCustom: 自定义蜜罐
      TemplateTypeFTP: FTP蜜罐
      TemplateTypeSSH: SSH蜜罐
      TemplateTypeTelnet: Telnet蜜罐
      TemplateTypeWeb: Web蜜罐
    x-enum-varnames:
    - TemplateTypeSSH
    - TemplateTypeWeb
    - TemplateTypeFTP
    - TemplateTypeTelnet
    - TemplateTypeCustom
  models.TemplateUpdateRequest:
    properties:
      config_schema:
        $ref: '#/definitions/models.ConfigSchema'
      default_config:
        $ref: '#/definitions/models.DefaultConfig'
      description:
        maxLength: 1000
        type: string
      name:
        maxLength: 100
        minLength: 1
        type: string
      resource_requirements:
        $ref: '#/definitions/models.ResourceRequirements'
      status:
        allOf:
        - $ref: '#/definitions/models.TemplateStatus'
        enum:
        - active
        - inactive
    type: object
  models.UserCreateRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      role:
        allOf:
        - $ref: '#/definitions/models.UserRole'
        enum:
        - administrator
        - operator
        - observer
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - password
    - role
    - username
    type: object
  models.UserInfo:
    properties:
      email:
        type: string
      id:
        type: integer
      role:
        $ref: '#/definitions/models.UserRole'
      role_name:
        type: string
      username:
        type: string
    type: object
  models.UserLoginRequest:
    properties:
      password:
        type: string
      username:
        type: string
    required:
    - password
    - username
    type: object
  models.UserLoginResponse:
    properties:
      expires_in:
        type: integer
      token:
        type: string
      user_info:
        $ref: '#/definitions/models.UserInfo'
    type: object
  models.UserRegisterRequest:
    properties:
      email:
        type: string
      password:
        minLength: 6
        type: string
      role:
        enum:
        - administrator
        - operator
        - observer
        type: string
      username:
        maxLength: 50
        minLength: 3
        type: string
    required:
    - email
    - password
    - username
    type: object
  models.UserRole:
    enum:
    - administrator
    - operator
    - observer
    type: string
    x-enum-comments:
      RoleAdministrator: 管理员 - 全部功能权限
      RoleObserver: 观察者 - 仅查看权限
      RoleOperator: 操作员 - 节点管理权限
    x-enum-varnames:
    - RoleAdministrator
    - RoleOperator
    - RoleObserver
  models.UserStatus:
    enum:
    - active
    - inactive
    type: string
    x-enum-comments:
      StatusActive: 活跃
      StatusInactive: 非活跃
    x-enum-varnames:
    - StatusActive
    - StatusInactive
  models.UserUpdateRequest:
    properties:
      email:
        type: string
      role:
        allOf:
        - $ref: '#/definitions/models.UserRole'
        enum:
        - administrator
        - operator
        - observer
      status:
        allOf:
        - $ref: '#/definitions/models.UserStatus'
        enum:
        - active
        - inactive
    type: object
host: localhost:8080
info:
  contact: {}
  description: 蜜罐管理平台API
  title: Honeypot Admin Panel API
  version: 1.0.0
paths:
  /api/v1/auth/change-password:
    post:
      consumes:
      - application/json
      description: 修改当前用户的密码
      parameters:
      - description: 密码修改信息
        in: body
        name: password
        required: true
        schema:
          $ref: '#/definitions/models.PasswordChangeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 修改密码
      tags:
      - 用户管理
  /api/v1/auth/login:
    post:
      consumes:
      - application/json
      description: 用户登录获取访问令牌
      parameters:
      - description: 登录信息
        in: body
        name: login
        required: true
        schema:
          $ref: '#/definitions/models.UserLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserLoginResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: 用户登录
      tags:
      - 认证
  /api/v1/auth/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfo'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取当前用户信息
      tags:
      - 用户管理
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: 注册新用户账户
      parameters:
      - description: 注册信息
        in: body
        name: register
        required: true
        schema:
          $ref: '#/definitions/models.UserRegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfo'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      summary: 用户注册
      tags:
      - 认证
  /api/v1/dashboard/attack-trends:
    get:
      consumes:
      - application/json
      description: 获取指定时间范围内的攻击趋势数据
      parameters:
      - default: 30
        description: 天数
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.AttackTrendsResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取攻击趋势
      tags:
      - 仪表板
  /api/v1/dashboard/export:
    get:
      consumes:
      - application/json
      description: 导出指定类型的数据报表
      parameters:
      - description: 报表类型
        enum:
        - overview
        - attacks
        - nodes
        - security
        in: query
        name: type
        required: true
        type: string
      - default: pdf
        description: 导出格式
        enum:
        - pdf
        - excel
        - csv
        in: query
        name: format
        type: string
      - default: 30
        description: 时间范围（天）
        in: query
        name: days
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 导出报表
      tags:
      - 仪表板
  /api/v1/dashboard/health:
    get:
      consumes:
      - application/json
      description: 获取详细的系统健康检查结果
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取系统健康状态
      tags:
      - 仪表板
  /api/v1/dashboard/node-status:
    get:
      consumes:
      - application/json
      description: 获取所有节点的状态统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.NodeStatusOverview'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点状态概览
      tags:
      - 仪表板
  /api/v1/dashboard/overview:
    get:
      consumes:
      - application/json
      description: 获取系统概览统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.DashboardOverview'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取仪表板概览
      tags:
      - 仪表板
  /api/v1/dashboard/performance:
    get:
      consumes:
      - application/json
      description: 获取系统性能指标信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.PerformanceMetrics'
              type: object
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取性能指标
      tags:
      - 仪表板
  /api/v1/dashboard/realtime:
    get:
      consumes:
      - application/json
      description: 获取实时的系统状态和攻击数据
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取实时数据
      tags:
      - 仪表板
  /api/v1/dashboard/security-alerts:
    get:
      consumes:
      - application/json
      description: 获取最新的安全告警信息
      parameters:
      - default: 20
        description: 返回数量限制
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SecurityAlertsResponse'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取安全告警
      tags:
      - 仪表板
  /api/v1/deployments:
    get:
      consumes:
      - application/json
      description: 分页获取服务部署列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: size
        type: integer
      - description: 节点ID
        in: query
        name: node_id
        type: string
      - description: 模板ID
        in: query
        name: template_id
        type: integer
      - description: 状态
        in: query
        name: status
        type: string
      - description: 关键词
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.PaginatedResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取部署列表
      tags:
      - 部署管理
    post:
      consumes:
      - application/json
      description: 创建新的服务部署
      parameters:
      - description: 部署信息
        in: body
        name: deployment
        required: true
        schema:
          $ref: '#/definitions/models.DeploymentCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerServiceDeployment'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 创建部署
      tags:
      - 部署管理
  /api/v1/deployments/{id}:
    delete:
      consumes:
      - application/json
      description: 删除服务部署
      parameters:
      - description: 部署ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 删除部署
      tags:
      - 部署管理
    get:
      consumes:
      - application/json
      description: 根据ID获取服务部署详细信息
      parameters:
      - description: 部署ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerServiceDeployment'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取部署详情
      tags:
      - 部署管理
    put:
      consumes:
      - application/json
      description: 更新服务部署配置
      parameters:
      - description: 部署ID
        in: path
        name: id
        required: true
        type: integer
      - description: 部署信息
        in: body
        name: deployment
        required: true
        schema:
          $ref: '#/definitions/models.DeploymentUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerServiceDeployment'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 更新部署
      tags:
      - 部署管理
  /api/v1/deployments/statistics:
    get:
      consumes:
      - application/json
      description: 获取部署统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.DeploymentStatistics'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取部署统计
      tags:
      - 部署管理
  /api/v1/nodes:
    get:
      consumes:
      - application/json
      description: 分页获取节点列表，支持过滤
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: size
        type: integer
      - description: 节点状态
        enum:
        - online
        - offline
        - error
        in: query
        name: status
        type: string
      - description: 节点区域
        in: query
        name: region
        type: string
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SwaggerNode'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点列表
      tags:
      - 节点管理
    post:
      consumes:
      - application/json
      description: 注册新的蜜罐节点
      parameters:
      - description: 节点注册信息
        in: body
        name: node
        required: true
        schema:
          $ref: '#/definitions/models.NodeRegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerNode'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 注册节点
      tags:
      - 节点管理
  /api/v1/nodes/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定节点
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 删除节点
      tags:
      - 节点管理
    get:
      consumes:
      - application/json
      description: 根据ID获取节点详细信息
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerNode'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点信息
      tags:
      - 节点管理
    put:
      consumes:
      - application/json
      description: 更新节点的基本信息
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新信息
        in: body
        name: node
        required: true
        schema:
          $ref: '#/definitions/models.NodeUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerNode'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 更新节点信息
      tags:
      - 节点管理
  /api/v1/nodes/{id}/deployments:
    get:
      consumes:
      - application/json
      description: 获取指定节点的服务部署列表
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SwaggerServiceDeployment'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点部署列表
      tags:
      - 节点管理
  /api/v1/nodes/{id}/metrics:
    get:
      consumes:
      - application/json
      description: 获取节点的实时指标信息
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点指标
      tags:
      - 节点管理
  /api/v1/nodes/{id}/regenerate-token:
    post:
      consumes:
      - application/json
      description: 为节点重新生成认证Token
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerNode'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 重新生成节点Token
      tags:
      - 节点管理
  /api/v1/nodes/{id}/status:
    put:
      consumes:
      - application/json
      description: 手动更新节点状态
      parameters:
      - description: 节点ID
        in: path
        name: id
        required: true
        type: string
      - description: 状态信息
        in: body
        name: status
        required: true
        schema:
          $ref: '#/definitions/models.NodeStatusUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 更新节点状态
      tags:
      - 节点管理
  /api/v1/nodes/{node_id}/command:
    post:
      consumes:
      - application/json
      description: 向指定节点发送控制命令
      parameters:
      - description: 节点ID
        in: path
        name: node_id
        required: true
        type: string
      - description: 命令信息
        in: body
        name: command
        required: true
        schema:
          $ref: '#/definitions/models.NodeCommandRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 发送节点命令
      tags:
      - WebSocket
  /api/v1/nodes/{node_id}/deploy:
    post:
      consumes:
      - application/json
      description: 向指定节点发送服务部署命令
      parameters:
      - description: 节点ID
        in: path
        name: node_id
        required: true
        type: string
      - description: 部署命令信息
        in: body
        name: command
        required: true
        schema:
          $ref: '#/definitions/models.DeployCommandRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 发送部署命令
      tags:
      - WebSocket
  /api/v1/nodes/stats:
    get:
      consumes:
      - application/json
      description: 获取节点的统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取节点统计信息
      tags:
      - 节点管理
  /api/v1/templates:
    get:
      consumes:
      - application/json
      description: 分页获取模板列表，支持过滤
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: size
        type: integer
      - description: 模板类型
        in: query
        name: type
        type: string
      - description: 模板状态
        in: query
        name: status
        type: string
      - description: 搜索关键词
        in: query
        name: keyword
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.SwaggerTemplate'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取模板列表
      tags:
      - 模板管理
    post:
      consumes:
      - application/json
      description: 创建新的蜜罐服务模板
      parameters:
      - description: 模板信息
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/models.TemplateCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerTemplate'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 创建模板
      tags:
      - 模板管理
  /api/v1/templates/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定模板
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 删除模板
      tags:
      - 模板管理
    get:
      consumes:
      - application/json
      description: 根据ID获取模板详细信息
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerTemplate'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取模板信息
      tags:
      - 模板管理
    put:
      consumes:
      - application/json
      description: 更新模板的基本信息
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新信息
        in: body
        name: template
        required: true
        schema:
          $ref: '#/definitions/models.TemplateUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerTemplate'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 更新模板信息
      tags:
      - 模板管理
  /api/v1/templates/{id}/pull:
    post:
      consumes:
      - application/json
      description: 为模板拉取对应的Docker镜像
      parameters:
      - description: 模板ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 拉取Docker镜像
      tags:
      - 模板管理
  /api/v1/templates/deploy:
    post:
      consumes:
      - application/json
      description: 将模板部署到指定节点
      parameters:
      - description: 部署信息
        in: body
        name: deployment
        required: true
        schema:
          $ref: '#/definitions/models.TemplateDeployRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.SwaggerServiceDeployment'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 部署模板
      tags:
      - 模板管理
  /api/v1/users:
    get:
      consumes:
      - application/json
      description: 分页获取用户列表
      parameters:
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页大小
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.PaginatedResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.UserInfo'
                  type: array
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取用户列表
      tags:
      - 用户管理
    post:
      consumes:
      - application/json
      description: 创建新用户账户（需要管理员权限）
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.UserCreateRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfo'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 创建用户
      tags:
      - 用户管理
  /api/v1/users/{id}:
    delete:
      consumes:
      - application/json
      description: 删除指定用户（需要管理员权限）
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 删除用户
      tags:
      - 用户管理
    get:
      consumes:
      - application/json
      description: 根据ID获取用户详细信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfo'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取用户信息
      tags:
      - 用户管理
    put:
      consumes:
      - application/json
      description: 更新用户的基本信息
      parameters:
      - description: 用户ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.UserUpdateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/handlers.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.UserInfo'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 更新用户信息
      tags:
      - 用户管理
  /api/v1/websocket/admin/{user_id}/message:
    post:
      consumes:
      - application/json
      description: 向指定管理员发送消息
      parameters:
      - description: 用户ID
        in: path
        name: user_id
        required: true
        type: string
      - description: 消息信息
        in: body
        name: message
        required: true
        schema:
          $ref: '#/definitions/models.AdminMessageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 发送消息给管理员
      tags:
      - WebSocket
  /api/v1/websocket/broadcast:
    post:
      consumes:
      - application/json
      description: 向所有管理员广播通知消息
      parameters:
      - description: 通知信息
        in: body
        name: notification
        required: true
        schema:
          $ref: '#/definitions/models.NotificationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 广播通知
      tags:
      - WebSocket
  /api/v1/websocket/status:
    get:
      consumes:
      - application/json
      description: 获取当前WebSocket连接的统计信息
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/handlers.ErrorResponse'
      security:
      - BearerAuth: []
      summary: 获取WebSocket连接状态
      tags:
      - WebSocket
  /ws/admin:
    get:
      consumes:
      - application/json
      description: 管理员通过WebSocket连接到管理平台
      produces:
      - application/json
      responses: {}
      security:
      - BearerAuth: []
      summary: 管理员WebSocket连接
      tags:
      - WebSocket
  /ws/node:
    get:
      consumes:
      - application/json
      description: 节点通过WebSocket连接到管理平台
      produces:
      - application/json
      responses: {}
      summary: 节点WebSocket连接
      tags:
      - WebSocket
schemes:
- http
- https
securityDefinitions:
  BearerAuth:
    description: JWT Token，格式为 "Bearer {token}"
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
