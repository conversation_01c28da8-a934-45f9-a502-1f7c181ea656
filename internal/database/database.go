package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"honeypot-admin/internal/models"

	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 全局数据库实例
var DB *gorm.DB

// Config 数据库配置结构
type Config struct {
	Host            string
	Port            int
	Username        string
	Password        string
	DBName          string
	Charset         string
	ParseTime       bool
	Loc             string
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime time.Duration
}

// DefaultConfig 返回默认数据库配置
func DefaultConfig() *Config {
	return &Config{
		Host:            getEnv("DB_HOST", "localhost"),
		Port:            getEnvAsInt("DB_PORT", 3306),
		Username:        getEnv("DB_USER", "honeypot"),
		Password:        getEnv("DB_PASSWORD", "honeypot123"),
		DBName:          getEnv("DB_NAME", "honeypot"),
		Charset:         "utf8mb4",
		ParseTime:       true,
		Loc:             "Local",
		MaxIdleConns:    10,
		MaxOpenConns:    100,
		ConnMaxLifetime: time.Hour,
	}
}

// Initialize 初始化数据库连接
func Initialize(config *Config) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.DBName,
		config.Charset,
		config.ParseTime,
		config.Loc,
	)

	// 配置GORM日志
	logLevel := logger.Info
	if getEnv("GIN_MODE", "debug") == "release" {
		logLevel = logger.Error
	}

	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	}

	var err error

	// 检查是否使用SQLite（通过文件路径判断）
	if config.DBName != "" && (config.Host == "" || config.Host == "localhost") && config.Port == 0 {
		// 使用SQLite，创建目录
		if dir := filepath.Dir(config.DBName); dir != "." {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("failed to create database directory: %w", err)
			}
		}
		DB, err = gorm.Open(sqlite.Open(config.DBName), gormConfig)
	} else {
		// 使用MySQL
		DB, err = gorm.Open(mysql.Open(dsn), gormConfig)
	}

	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(config.MaxIdleConns)
	sqlDB.SetMaxOpenConns(config.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(config.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connected successfully")
	return nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 迁移所有模型
	err := DB.AutoMigrate(
		&models.User{},
		&models.Node{},
		&models.Template{},
		&models.ServiceDeployment{},
	)

	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// CreateDefaultData 创建默认数据
func CreateDefaultData() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 创建默认管理员用户
	var adminUser models.User
	result := DB.Where("username = ?", "admin").First(&adminUser)
	if result.Error == gorm.ErrRecordNotFound {
		// 这里应该使用bcrypt加密密码，暂时使用明文
		adminUser = models.User{
			Username: "admin",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Email:    "<EMAIL>",
			Role:     models.RoleAdministrator,
			Status:   models.StatusActive,
		}
		if err := DB.Create(&adminUser).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %w", err)
		}
		log.Println("Default admin user created")
	}

	// 创建默认模板
	templates := []models.Template{
		{
			ID:          "template_ssh_001",
			Name:        "SSH蜜罐模板",
			Type:        models.TemplateTypeSSH,
			Description: "基于Cowrie的SSH蜜罐",
			ImageName:   "honeypot/ssh-cowrie",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "SSH端口",
					Default:     22,
					Required:    true,
				},
				"banner": models.ConfigParam{
					Type:        "string",
					Description: "SSH横幅",
					Default:     "OpenSSH_7.4",
					Required:    false,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":   22,
				"banner": "OpenSSH_7.4",
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "128Mi",
				CPU:    "100m",
			},
		},
		{
			ID:          "template_web_001",
			Name:        "Web蜜罐模板",
			Type:        models.TemplateTypeWeb,
			Description: "基于Dionaea的Web蜜罐",
			ImageName:   "honeypot/web-dionaea",
			ImageTag:    "latest",
			Version:     "1.0.0",
			Status:      models.TemplateStatusActive,
			ConfigSchema: models.ConfigSchema{
				"port": models.ConfigParam{
					Type:        "integer",
					Description: "Web端口",
					Default:     80,
					Required:    true,
				},
				"server_name": models.ConfigParam{
					Type:        "string",
					Description: "服务器名称",
					Default:     "Apache/2.4.41",
					Required:    false,
				},
			},
			DefaultConfig: models.DefaultConfig{
				"port":        80,
				"server_name": "Apache/2.4.41",
			},
			ResourceRequirements: &models.ResourceRequirements{
				Memory: "256Mi",
				CPU:    "200m",
			},
		},
	}

	for _, template := range templates {
		var existingTemplate models.Template
		result := DB.Where("id = ?", template.ID).First(&existingTemplate)
		if result.Error == gorm.ErrRecordNotFound {
			if err := DB.Create(&template).Error; err != nil {
				return fmt.Errorf("failed to create template %s: %w", template.ID, err)
			}
			log.Printf("Default template %s created", template.ID)
		}
	}

	return nil
}

// Close 关闭数据库连接
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// HealthCheck 数据库健康检查
func HealthCheck() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// 辅助函数

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := fmt.Sscanf(value, "%d", &defaultValue); err == nil && intValue == 1 {
			return defaultValue
		}
	}
	return defaultValue
}
