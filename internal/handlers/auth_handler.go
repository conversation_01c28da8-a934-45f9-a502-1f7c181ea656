package handlers

import (
	"net/http"
	"strconv"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *services.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录获取访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param login body models.UserLoginRequest true "登录信息"
// @Success 200 {object} Response{data=models.UserLoginResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 执行登录
	response, err := h.authService.Login(&req)
	if err != nil {
		switch err {
		case services.ErrUserNotFound, services.ErrInvalidPassword:
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_credentials",
				Message: "用户名或密码错误",
			})
		case services.ErrUserInactive:
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "user_inactive",
				Message: "用户账户已被禁用",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "login_failed",
				Message: "登录失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "登录成功",
		Data:    response,
	})
}

// Register 用户注册
// @Summary 用户注册
// @Description 注册新用户账户
// @Tags 认证
// @Accept json
// @Produce json
// @Param register body models.UserRegisterRequest true "注册信息"
// @Success 201 {object} Response{data=models.UserInfo}
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 执行注册
	user, err := h.authService.Register(&req)
	if err != nil {
		switch err {
		case services.ErrUsernameExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "username_exists",
				Message: "用户名已存在",
			})
		case services.ErrEmailExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "email_exists",
				Message: "邮箱已存在",
			})
		case services.ErrWeakPassword:
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "weak_password",
				Message: "密码强度不足",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "register_failed",
				Message: "注册失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "注册成功",
		Data:    user.ToUserInfo(),
	})
}

// CreateUser 创建用户
// @Summary 创建用户
// @Description 创建新用户账户（需要管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user body models.UserCreateRequest true "用户信息"
// @Success 201 {object} Response{data=models.UserInfo}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users [post]
func (h *AuthHandler) CreateUser(c *gin.Context) {
	var req models.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 创建用户
	user, err := h.authService.CreateUser(&req)
	if err != nil {
		switch err {
		case services.ErrUsernameExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "username_exists",
				Message: "用户名已存在",
			})
		case services.ErrEmailExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "email_exists",
				Message: "邮箱已存在",
			})
		case services.ErrWeakPassword:
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "weak_password",
				Message: "密码强度不足",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "create_failed",
				Message: "创建用户失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "用户创建成功",
		Data:    user.ToUserInfo(),
	})
}

// GetUser 获取用户信息
// @Summary 获取用户信息
// @Description 根据ID获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} Response{data=models.UserInfo}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users/{id} [get]
func (h *AuthHandler) GetUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的用户ID",
		})
		return
	}

	user, err := h.authService.GetUserByID(uint(id))
	if err != nil {
		if err == services.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "user_not_found",
				Message: "用户不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    user.ToUserInfo(),
	})
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户的基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Param user body models.UserUpdateRequest true "更新信息"
// @Success 200 {object} Response{data=models.UserInfo}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users/{id} [put]
func (h *AuthHandler) UpdateUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的用户ID",
		})
		return
	}

	var req models.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	user, err := h.authService.UpdateUser(uint(id), &req)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "user_not_found",
				Message: "用户不存在",
			})
		case services.ErrEmailExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "email_exists",
				Message: "邮箱已存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "update_failed",
				Message: "更新用户失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新成功",
		Data:    user.ToUserInfo(),
	})
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除指定用户（需要管理员权限）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 403 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users/{id} [delete]
func (h *AuthHandler) DeleteUser(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的用户ID",
		})
		return
	}

	// 检查是否尝试删除自己
	currentUserID, exists := c.Get("user_id")
	if exists && currentUserID.(uint) == uint(id) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "cannot_delete_self",
			Message: "不能删除自己的账户",
		})
		return
	}

	err = h.authService.DeleteUser(uint(id))
	if err != nil {
		if err == services.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "user_not_found",
				Message: "用户不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "delete_failed",
			Message: "删除用户失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除成功",
	})
}

// ListUsers 获取用户列表
// @Summary 获取用户列表
// @Description 分页获取用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Success 200 {object} PaginatedResponse{data=[]models.UserInfo}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/users [get]
func (h *AuthHandler) ListUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	users, total, err := h.authService.ListUsers(page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询用户列表失败: " + err.Error(),
		})
		return
	}

	// 转换为响应格式
	userInfos := make([]*models.UserInfo, len(users))
	for i, user := range users {
		userInfos[i] = user.ToUserInfo()
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Success: true,
		Message: "查询成功",
		Data:    userInfos,
		Pagination: PaginationInfo{
			Page:       page,
			Size:       size,
			Total:      total,
			TotalPages: (total + int64(size) - 1) / int64(size),
		},
	})
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改当前用户的密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param password body models.PasswordChangeRequest true "密码修改信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req models.PasswordChangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "未授权访问",
		})
		return
	}

	err := h.authService.ChangePassword(userID.(uint), &req)
	if err != nil {
		switch err {
		case services.ErrUserNotFound:
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "user_not_found",
				Message: "用户不存在",
			})
		case services.ErrInvalidPassword:
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "invalid_old_password",
				Message: "原密码错误",
			})
		case services.ErrWeakPassword:
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "weak_password",
				Message: "新密码强度不足",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "change_password_failed",
				Message: "修改密码失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "密码修改成功",
	})
}

// GetProfile 获取当前用户信息
// @Summary 获取当前用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.UserInfo}
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "未授权访问",
		})
		return
	}

	user, err := h.authService.GetUserByID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询用户信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    user.ToUserInfo(),
	})
}
