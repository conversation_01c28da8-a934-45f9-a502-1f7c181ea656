package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// DashboardHandler 仪表板处理器
type DashboardHandler struct {
	dashboardService *services.DashboardService
}

// NewDashboardHandler 创建仪表板处理器
func NewDashboardHandler(dashboardService *services.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetOverview 获取概览信息
// @Summary 获取仪表板概览
// @Description 获取系统概览统计信息
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.DashboardOverview}
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/overview [get]
func (h *DashboardHandler) GetOverview(c *gin.Context) {
	overview, err := h.dashboardService.GetOverviewStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取概览信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取概览信息成功",
		Data:    overview,
	})
}

// GetAttackTrends 获取攻击趋势
// @Summary 获取攻击趋势
// @Description 获取指定时间范围内的攻击趋势数据
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param days query int false "天数" default(30)
// @Success 200 {object} Response{data=models.AttackTrendsResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/attack-trends [get]
func (h *DashboardHandler) GetAttackTrends(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}
	if days > 365 {
		days = 365
	}

	trends, err := h.dashboardService.GetAttackTrends(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取攻击趋势失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取攻击趋势成功",
		Data:    trends,
	})
}

// GetNodeStatus 获取节点状态概览
// @Summary 获取节点状态概览
// @Description 获取所有节点的状态统计信息
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.NodeStatusOverview}
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/node-status [get]
func (h *DashboardHandler) GetNodeStatus(c *gin.Context) {
	nodeStatus, err := h.dashboardService.GetNodeStatusOverview()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取节点状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取节点状态成功",
		Data:    nodeStatus,
	})
}

// GetSecurityAlerts 获取安全告警
// @Summary 获取安全告警
// @Description 获取最新的安全告警信息
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "返回数量限制" default(20)
// @Success 200 {object} Response{data=models.SecurityAlertsResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/security-alerts [get]
func (h *DashboardHandler) GetSecurityAlerts(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	alerts, err := h.dashboardService.GetSecurityAlerts(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取安全告警失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取安全告警成功",
		Data:    alerts,
	})
}

// GetPerformanceMetrics 获取性能指标
// @Summary 获取性能指标
// @Description 获取系统性能指标信息
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.PerformanceMetrics}
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/performance [get]
func (h *DashboardHandler) GetPerformanceMetrics(c *gin.Context) {
	metrics, err := h.dashboardService.GetPerformanceMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取性能指标失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取性能指标成功",
		Data:    metrics,
	})
}

// GetRealtimeData 获取实时数据
// @Summary 获取实时数据
// @Description 获取实时的系统状态和攻击数据
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/realtime [get]
func (h *DashboardHandler) GetRealtimeData(c *gin.Context) {
	// 获取实时概览数据
	overview, err := h.dashboardService.GetOverviewStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取实时数据失败: " + err.Error(),
		})
		return
	}

	// 获取最新的安全告警
	alerts, err := h.dashboardService.GetSecurityAlerts(5)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取安全告警失败: " + err.Error(),
		})
		return
	}

	// 组合实时数据
	realtimeData := map[string]interface{}{
		"overview":      overview,
		"recent_alerts": alerts,
		"last_updated":  overview.SystemStatus.UpdatedAt,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取实时数据成功",
		Data:    realtimeData,
	})
}

// ExportReport 导出报表
// @Summary 导出报表
// @Description 导出指定类型的数据报表
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param type query string true "报表类型" Enums(overview, attacks, nodes, security)
// @Param format query string false "导出格式" default(pdf) Enums(pdf, excel, csv)
// @Param days query int false "时间范围（天）" default(30)
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/export [get]
func (h *DashboardHandler) ExportReport(c *gin.Context) {
	reportType := c.Query("type")
	format := c.DefaultQuery("format", "pdf")
	daysStr := c.DefaultQuery("days", "30")

	// 验证参数
	validTypes := map[string]bool{
		"overview": true,
		"attacks":  true,
		"nodes":    true,
		"security": true,
	}
	if !validTypes[reportType] {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_type",
			Message: "无效的报表类型",
		})
		return
	}

	validFormats := map[string]bool{
		"pdf":   true,
		"excel": true,
		"csv":   true,
	}
	if !validFormats[format] {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_format",
			Message: "无效的导出格式",
		})
		return
	}

	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 30
	}

	// 这里应该实现实际的报表生成逻辑
	// 简化实现，返回成功响应
	filename := generateReportFilename(reportType, format)

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "报表生成成功",
		Data: map[string]interface{}{
			"filename":     filename,
			"type":         reportType,
			"format":       format,
			"days":         days,
			"download_url": "/api/v1/downloads/" + filename,
			"generated_at": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// GetSystemHealth 获取系统健康状态
// @Summary 获取系统健康状态
// @Description 获取详细的系统健康检查结果
// @Tags 仪表板
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/dashboard/health [get]
func (h *DashboardHandler) GetSystemHealth(c *gin.Context) {
	// 获取性能指标
	metrics, err := h.dashboardService.GetPerformanceMetrics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取系统健康状态失败: " + err.Error(),
		})
		return
	}

	// 获取概览信息中的系统状态
	overview, err := h.dashboardService.GetOverviewStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取系统状态失败: " + err.Error(),
		})
		return
	}

	healthData := map[string]interface{}{
		"system_status":       overview.SystemStatus,
		"performance_metrics": metrics,
		"database_health":     metrics.DatabaseStats,
		"storage_usage":       metrics.StorageStats,
		"processing_stats":    metrics.ProcessingStats,
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取系统健康状态成功",
		Data:    healthData,
	})
}

// 辅助函数

func generateReportFilename(reportType, format string) string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("honeypot_report_%s_%s.%s", reportType, timestamp, format)
}
