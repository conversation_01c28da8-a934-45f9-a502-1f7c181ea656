package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// DeploymentHandler 部署处理器
type DeploymentHandler struct {
	deploymentService *services.DeploymentService
	nodeService       *services.NodeService
	templateService   *services.TemplateService
}

// NewDeploymentHandler 创建部署处理器
func NewDeploymentHandler(
	deploymentService *services.DeploymentService,
	nodeService *services.NodeService,
	templateService *services.TemplateService,
) *DeploymentHandler {
	return &DeploymentHandler{
		deploymentService: deploymentService,
		nodeService:       nodeService,
		templateService:   templateService,
	}
}

// ListDeployments 获取部署列表
// @Summary 获取部署列表
// @Description 分页获取服务部署列表
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(20)
// @Param node_id query string false "节点ID"
// @Param template_id query int false "模板ID"
// @Param status query string false "状态"
// @Param keyword query string false "关键词"
// @Success 200 {object} PaginatedResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments [get]
func (h *DeploymentHandler) ListDeployments(c *gin.Context) {
	var req models.DeploymentListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	deployments, total, err := h.deploymentService.ListDeployments(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询部署列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Success: true,
		Message: "获取部署列表成功",
		Data:    deployments,
		Pagination: PaginationInfo{
			Page:       req.Page,
			Size:       req.Size,
			Total:      total,
			TotalPages: (total + int64(req.Size) - 1) / int64(req.Size),
		},
	})
}

// GetDeployment 获取部署详情
// @Summary 获取部署详情
// @Description 根据ID获取服务部署详细信息
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "部署ID"
// @Success 200 {object} Response{data=models.SwaggerServiceDeployment}
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments/{id} [get]
func (h *DeploymentHandler) GetDeployment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的部署ID",
		})
		return
	}

	deployment, err := h.deploymentService.GetDeployment(uint(id))
	if err != nil {
		if err == services.ErrDeploymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "deployment_not_found",
				Message: "部署不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询部署失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取部署详情成功",
		Data:    deployment,
	})
}

// CreateDeployment 创建部署
// @Summary 创建部署
// @Description 创建新的服务部署
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param deployment body models.DeploymentCreateRequest true "部署信息"
// @Success 201 {object} Response{data=models.SwaggerServiceDeployment}
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments [post]
func (h *DeploymentHandler) CreateDeployment(c *gin.Context) {
	var req models.DeploymentCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 验证节点是否存在
	_, err := h.nodeService.GetNode(req.NodeID)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点失败: " + err.Error(),
		})
		return
	}

	// 验证模板是否存在
	template, err := h.templateService.GetTemplate(strconv.FormatUint(uint64(req.TemplateID), 10))
	if err != nil {
		if err == services.ErrTemplateNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询模板失败: " + err.Error(),
		})
		return
	}

	// 序列化配置
	configJSON, _ := json.Marshal(req.Config)
	portsJSON, _ := json.Marshal(req.Ports)
	envJSON, _ := json.Marshal(req.Environment)
	volumesJSON, _ := json.Marshal(req.Volumes)

	// 创建部署记录
	deployment := &models.ServiceDeployment{
		Name:        req.Name,
		NodeID:      req.NodeID,
		TemplateID:  req.TemplateID,
		ImageName:   template.ImageName + ":" + template.ImageTag,
		Status:      "pending",
		Config:      string(configJSON),
		Ports:       string(portsJSON),
		Environment: string(envJSON),
		Volumes:     string(volumesJSON),
		CreatedBy:   "admin", // TODO: 从JWT中获取用户信息
	}

	if err := h.deploymentService.CreateDeployment(deployment); err != nil {
		if err == services.ErrDeploymentExists {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "deployment_exists",
				Message: "部署已存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "create_failed",
			Message: "创建部署失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "创建部署成功",
		Data:    deployment,
	})
}

// UpdateDeployment 更新部署
// @Summary 更新部署
// @Description 更新服务部署配置
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "部署ID"
// @Param deployment body models.DeploymentUpdateRequest true "部署信息"
// @Success 200 {object} Response{data=models.SwaggerServiceDeployment}
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments/{id} [put]
func (h *DeploymentHandler) UpdateDeployment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的部署ID",
		})
		return
	}

	var req models.DeploymentUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 获取现有部署
	deployment, err := h.deploymentService.GetDeployment(uint(id))
	if err != nil {
		if err == services.ErrDeploymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "deployment_not_found",
				Message: "部署不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询部署失败: " + err.Error(),
		})
		return
	}

	// TODO: 实现部署更新逻辑
	// 这里需要通过WebSocket向节点发送更新命令

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新部署成功",
		Data:    deployment,
	})
}

// DeleteDeployment 删除部署
// @Summary 删除部署
// @Description 删除服务部署
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "部署ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments/{id} [delete]
func (h *DeploymentHandler) DeleteDeployment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "无效的部署ID",
		})
		return
	}

	// 检查部署是否存在且可以删除
	deployment, err := h.deploymentService.GetDeployment(uint(id))
	if err != nil {
		if err == services.ErrDeploymentNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "deployment_not_found",
				Message: "部署不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询部署失败: " + err.Error(),
		})
		return
	}

	if !deployment.CanDelete() {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "cannot_delete",
			Message: "运行中的部署不能删除，请先停止服务",
		})
		return
	}

	if err := h.deploymentService.DeleteDeployment(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "delete_failed",
			Message: "删除部署失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除部署成功",
	})
}

// GetDeploymentStatistics 获取部署统计
// @Summary 获取部署统计
// @Description 获取部署统计信息
// @Tags 部署管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=models.DeploymentStatistics}
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/deployments/statistics [get]
func (h *DeploymentHandler) GetDeploymentStatistics(c *gin.Context) {
	stats, err := h.deploymentService.GetDeploymentStatistics()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "获取部署统计失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取部署统计成功",
		Data:    stats,
	})
}
