package handlers

import (
	"net/http"
	"strconv"
	"time"

	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// IntelligenceHandler 情报数据处理器
type IntelligenceHandler struct {
	decoyWatchService     *services.DecoyWatchService
	attackAnalysisService *services.AttackAnalysisService
}

// NewIntelligenceHandler 创建情报数据处理器
func NewIntelligenceHandler(decoyWatchService *services.DecoyWatchService, attackAnalysisService *services.AttackAnalysisService) *IntelligenceHandler {
	return &IntelligenceHandler{
		decoyWatchService:     decoyWatchService,
		attackAnalysisService: attackAnalysisService,
	}
}

// GetAttackStatistics 获取攻击统计数据
// @Summary 获取攻击统计数据
// @Description 从DecoyWatch系统获取攻击统计数据，支持按节点和时间范围过滤
// @Tags 情报数据
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param node_id query string false "节点ID"
// @Param start_time query string false "开始时间 (RFC3339格式)"
// @Param end_time query string false "结束时间 (RFC3339格式)"
// @Success 200 {object} Response{data=services.AttackStatisticsResponse}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/statistics [get]
func (h *IntelligenceHandler) GetAttackStatistics(c *gin.Context) {
	// 检查DecoyWatch服务是否启用
	if !h.decoyWatchService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unavailable",
			Message: "DecoyWatch服务未配置或未启用",
		})
		return
	}

	// 解析查询参数
	nodeID := c.Query("node_id")
	startTimeStr := c.Query("start_time")
	endTimeStr := c.Query("end_time")

	var startTime, endTime *time.Time

	// 解析开始时间
	if startTimeStr != "" {
		t, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "invalid_start_time",
				Message: "开始时间格式无效，请使用RFC3339格式",
			})
			return
		}
		startTime = &t
	}

	// 解析结束时间
	if endTimeStr != "" {
		t, err := time.Parse(time.RFC3339, endTimeStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Success: false,
				Error:   "invalid_end_time",
				Message: "结束时间格式无效，请使用RFC3339格式",
			})
			return
		}
		endTime = &t
	}

	// 从DecoyWatch获取统计数据
	stats, err := h.decoyWatchService.GetAttackStatistics(c.Request.Context(), nodeID, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "decoywatch_error",
			Message: "获取攻击统计数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取攻击统计数据成功",
		Data:    stats,
	})
}

// GetIPStatistics 获取IP统计数据
// @Summary 获取IP统计数据
// @Description 从DecoyWatch系统获取IP统计数据
// @Tags 情报数据
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param limit query int false "返回数量限制" default(10)
// @Success 200 {object} Response{data=[]services.SourceIPCount}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/ip-statistics [get]
func (h *IntelligenceHandler) GetIPStatistics(c *gin.Context) {
	// 检查DecoyWatch服务是否启用
	if !h.decoyWatchService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unavailable",
			Message: "DecoyWatch服务未配置或未启用",
		})
		return
	}

	// 解析limit参数
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Error:   "invalid_limit",
			Message: "limit参数必须是正整数",
		})
		return
	}

	// 从DecoyWatch获取IP统计数据
	ipStats, err := h.decoyWatchService.GetIPStatistics(c.Request.Context(), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "decoywatch_error",
			Message: "获取IP统计数据失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取IP统计数据成功",
		Data:    ipStats,
	})
}

// SendAttackEvent 发送攻击事件到DecoyWatch
// @Summary 发送攻击事件
// @Description 将攻击事件数据发送到DecoyWatch系统进行存储和分析
// @Tags 情报数据
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param event body services.AttackEventRequest true "攻击事件数据"
// @Success 201 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/attack-events [post]
func (h *IntelligenceHandler) SendAttackEvent(c *gin.Context) {
	// 检查DecoyWatch服务是否启用
	if !h.decoyWatchService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unavailable",
			Message: "DecoyWatch服务未配置或未启用",
		})
		return
	}

	var event services.AttackEventRequest
	if err := c.ShouldBindJSON(&event); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Error:   "invalid_request",
			Message: "请求数据格式无效: " + err.Error(),
		})
		return
	}

	// 发送到DecoyWatch
	if err := h.decoyWatchService.SendAttackEvent(c.Request.Context(), &event); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "decoywatch_error",
			Message: "发送攻击事件失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "攻击事件发送成功",
	})
}

// SendAttackEventsBatch 批量发送攻击事件到DecoyWatch
// @Summary 批量发送攻击事件
// @Description 批量将攻击事件数据发送到DecoyWatch系统
// @Tags 情报数据
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param events body []services.AttackEventRequest true "攻击事件数据列表"
// @Success 201 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/attack-events/batch [post]
func (h *IntelligenceHandler) SendAttackEventsBatch(c *gin.Context) {
	// 检查DecoyWatch服务是否启用
	if !h.decoyWatchService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unavailable",
			Message: "DecoyWatch服务未配置或未启用",
		})
		return
	}

	var events []*services.AttackEventRequest
	if err := c.ShouldBindJSON(&events); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Error:   "invalid_request",
			Message: "请求数据格式无效: " + err.Error(),
		})
		return
	}

	if len(events) == 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Error:   "empty_events",
			Message: "攻击事件列表不能为空",
		})
		return
	}

	// 批量发送到DecoyWatch
	if err := h.decoyWatchService.SendAttackEventsBatch(c.Request.Context(), events); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "decoywatch_error",
			Message: "批量发送攻击事件失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "批量攻击事件发送成功",
		Data: map[string]interface{}{
			"count": len(events),
		},
	})
}

// CheckDecoyWatchHealth 检查DecoyWatch服务健康状态
// @Summary 检查DecoyWatch健康状态
// @Description 检查DecoyWatch情报收集系统的健康状态
// @Tags 情报数据
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response{data=map[string]interface{}}
// @Failure 401 {object} ErrorResponse
// @Failure 503 {object} ErrorResponse
// @Router /api/v1/intelligence/health [get]
func (h *IntelligenceHandler) CheckDecoyWatchHealth(c *gin.Context) {
	// 检查DecoyWatch服务是否启用
	if !h.decoyWatchService.IsEnabled() {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unavailable",
			Message: "DecoyWatch服务未配置或未启用",
		})
		return
	}

	// 检查健康状态
	if err := h.decoyWatchService.HealthCheck(c.Request.Context()); err != nil {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Success: false,
			Error:   "service_unhealthy",
			Message: "DecoyWatch服务不健康: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "DecoyWatch服务健康",
		Data: map[string]interface{}{
			"status":     "healthy",
			"service":    "DecoyWatch",
			"checked_at": time.Now(),
		},
	})
}

// GetAttackTrendAnalysis 获取攻击趋势分析
// @Summary 获取攻击趋势分析
// @Description 分析攻击趋势，包括时间分布、增长率和高峰时段
// @Tags 情报分析
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param time_range query string false "时间范围" Enums(1h,24h,7d,30d) default(24h)
// @Param node_id query string false "节点ID"
// @Success 200 {object} Response{data=services.AttackTrendAnalysis}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/analysis/trends [get]
func (h *IntelligenceHandler) GetAttackTrendAnalysis(c *gin.Context) {
	timeRange := c.DefaultQuery("time_range", "24h")
	nodeID := c.Query("node_id")

	analysis, err := h.attackAnalysisService.AnalyzeAttackTrends(c.Request.Context(), timeRange, nodeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "analysis_error",
			Message: "攻击趋势分析失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "攻击趋势分析成功",
		Data:    analysis,
	})
}

// GetIPGeographicAnalysis 获取IP地理分布分析
// @Summary 获取IP地理分布分析
// @Description 分析攻击源IP的地理分布，包括国家、地区统计和热力图数据
// @Tags 情报分析
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param time_range query string false "时间范围" Enums(1h,24h,7d,30d) default(24h)
// @Param node_id query string false "节点ID"
// @Success 200 {object} Response{data=services.IPGeographicAnalysis}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/analysis/geographic [get]
func (h *IntelligenceHandler) GetIPGeographicAnalysis(c *gin.Context) {
	timeRange := c.DefaultQuery("time_range", "24h")
	nodeID := c.Query("node_id")

	analysis, err := h.attackAnalysisService.AnalyzeIPGeographic(c.Request.Context(), timeRange, nodeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "analysis_error",
			Message: "IP地理分布分析失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "IP地理分布分析成功",
		Data:    analysis,
	})
}

// GetAttackTypeAnalysis 获取攻击类型分析
// @Summary 获取攻击类型分析
// @Description 分析攻击类型分布、趋势和威胁评估
// @Tags 情报分析
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param time_range query string false "时间范围" Enums(1h,24h,7d,30d) default(24h)
// @Param node_id query string false "节点ID"
// @Success 200 {object} Response{data=services.AttackTypeAnalysis}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/intelligence/analysis/attack-types [get]
func (h *IntelligenceHandler) GetAttackTypeAnalysis(c *gin.Context) {
	timeRange := c.DefaultQuery("time_range", "24h")
	nodeID := c.Query("node_id")

	analysis, err := h.attackAnalysisService.AnalyzeAttackTypes(c.Request.Context(), timeRange, nodeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Error:   "analysis_error",
			Message: "攻击类型分析失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "攻击类型分析成功",
		Data:    analysis,
	})
}
