package handlers

import (
	"net/http"
	"strconv"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// NodeHandler 节点处理器
type NodeHandler struct {
	nodeService *services.NodeService
}

// NewNodeHandler 创建节点处理器
func NewNodeHandler(nodeService *services.NodeService) *NodeHandler {
	return &NodeHandler{
		nodeService: nodeService,
	}
}

// RegisterNode 注册节点
// @Summary 注册节点
// @Description 注册新的蜜罐节点
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param node body models.NodeRegisterRequest true "节点注册信息"
// @Success 201 {object} Response{data=models.SwaggerNode}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes [post]
func (h *NodeHandler) RegisterNode(c *gin.Context) {
	var req models.NodeRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	node, err := h.nodeService.RegisterNode(&req)
	if err != nil {
		switch err {
		case services.ErrNodeExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "node_exists",
				Message: "节点已存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "register_failed",
				Message: "注册节点失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "节点注册成功",
		Data:    node,
	})
}

// GetNode 获取节点信息
// @Summary 获取节点信息
// @Description 根据ID获取节点详细信息
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Success 200 {object} Response{data=models.SwaggerNode}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id} [get]
func (h *NodeHandler) GetNode(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	node, err := h.nodeService.GetNode(id)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    node,
	})
}

// UpdateNode 更新节点信息
// @Summary 更新节点信息
// @Description 更新节点的基本信息
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Param node body models.NodeUpdateRequest true "更新信息"
// @Success 200 {object} Response{data=models.SwaggerNode}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id} [put]
func (h *NodeHandler) UpdateNode(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	var req models.NodeUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	node, err := h.nodeService.UpdateNode(id, &req)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "update_failed",
			Message: "更新节点失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新成功",
		Data:    node,
	})
}

// DeleteNode 删除节点
// @Summary 删除节点
// @Description 删除指定节点
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id} [delete]
func (h *NodeHandler) DeleteNode(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	err := h.nodeService.DeleteNode(id)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "delete_failed",
			Message: "删除节点失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除成功",
	})
}

// ListNodes 获取节点列表
// @Summary 获取节点列表
// @Description 分页获取节点列表，支持过滤
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Param status query string false "节点状态" Enums(online, offline, error)
// @Param region query string false "节点区域"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} PaginatedResponse{data=[]models.SwaggerNode}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes [get]
func (h *NodeHandler) ListNodes(c *gin.Context) {
	var req models.NodeListRequest

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	req.Page = page
	req.Size = size
	req.Status = models.NodeStatus(c.Query("status"))
	req.Region = c.Query("region")
	req.Keyword = c.Query("keyword")

	nodes, total, err := h.nodeService.ListNodes(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Success: true,
		Message: "查询成功",
		Data:    nodes,
		Pagination: PaginationInfo{
			Page:       page,
			Size:       size,
			Total:      total,
			TotalPages: (total + int64(size) - 1) / int64(size),
		},
	})
}

// UpdateNodeStatus 更新节点状态
// @Summary 更新节点状态
// @Description 手动更新节点状态
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Param status body models.NodeStatusUpdateRequest true "状态信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id}/status [put]
func (h *NodeHandler) UpdateNodeStatus(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	var req models.NodeStatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	err := h.nodeService.UpdateNodeStatus(id, models.NodeStatus(req.Status))
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "update_failed",
			Message: "更新节点状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "状态更新成功",
	})
}

// RegenerateToken 重新生成节点Token
// @Summary 重新生成节点Token
// @Description 为节点重新生成认证Token
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Success 200 {object} Response{data=models.SwaggerNode}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id}/regenerate-token [post]
func (h *NodeHandler) RegenerateToken(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	node, err := h.nodeService.RegenerateToken(id)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "regenerate_failed",
			Message: "重新生成Token失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "Token重新生成成功",
		Data:    node,
	})
}

// GetNodeStats 获取节点统计信息
// @Summary 获取节点统计信息
// @Description 获取节点的统计信息
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/stats [get]
func (h *NodeHandler) GetNodeStats(c *gin.Context) {
	stats := h.nodeService.GetNodeStats()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取统计信息成功",
		Data:    stats,
	})
}

// GetNodeDeployments 获取节点部署列表
// @Summary 获取节点部署列表
// @Description 获取指定节点的服务部署列表
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Success 200 {object} Response{data=[]models.SwaggerServiceDeployment}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id}/deployments [get]
func (h *NodeHandler) GetNodeDeployments(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	deployments, err := h.nodeService.GetNodeDeployments(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点部署失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    deployments,
	})
}

// GetNodeMetrics 获取节点指标
// @Summary 获取节点指标
// @Description 获取节点的实时指标信息
// @Tags 节点管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "节点ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{id}/metrics [get]
func (h *NodeHandler) GetNodeMetrics(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_id",
			Message: "节点ID不能为空",
		})
		return
	}

	metrics, err := h.nodeService.GetNodeMetrics(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点指标失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    metrics,
	})
}
