package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// TemplateHandler 模板处理器
type TemplateHandler struct {
	templateService *services.TemplateService
}

// NewTemplateHandler 创建模板处理器
func NewTemplateHandler(templateService *services.TemplateService) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
	}
}

// CreateTemplate 创建模板
// @Summary 创建模板
// @Description 创建新的蜜罐服务模板
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param template body models.TemplateCreateRequest true "模板信息"
// @Success 201 {object} Response{data=models.SwaggerTemplate}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates [post]
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req models.TemplateCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 生成模板ID
	templateID := fmt.Sprintf("tpl-%d", time.Now().Unix())

	// 转换为Template模型
	template := &models.Template{
		ID:                   templateID,
		Name:                 req.Name,
		Description:          req.Description,
		Type:                 req.Type,
		ImageName:            req.ImageName,
		ImageTag:             req.ImageTag,
		Version:              req.Version,
		Status:               models.TemplateStatusActive,
		Active:               true,
		ConfigSchema:         req.ConfigSchema,
		DefaultConfig:        req.DefaultConfig,
		ResourceRequirements: req.ResourceRequirements,
	}

	err := h.templateService.CreateTemplate(template)
	if err != nil {
		switch err {
		case services.ErrTemplateExists:
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "template_exists",
				Message: "模板已存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "create_failed",
				Message: "创建模板失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusCreated, Response{
		Success: true,
		Message: "模板创建成功",
		Data:    template,
	})
}

// PullDockerImage 拉取Docker镜像
// @Summary 拉取Docker镜像
// @Description 为模板拉取对应的Docker镜像
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates/{id}/pull [post]
func (h *TemplateHandler) PullDockerImage(c *gin.Context) {
	idStr := c.Param("id")

	// 获取模板信息
	template, err := h.templateService.GetTemplate(idStr)
	if err != nil {
		if err == services.ErrTemplateNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "get_template_failed",
				Message: "获取模板失败: " + err.Error(),
			})
		}
		return
	}

	// 异步拉取镜像
	go func() {
		if err := h.templateService.PullDockerImage(template.ImageName); err != nil {
			// 记录错误日志
			// log.Printf("Failed to pull image %s: %v", template.ImageName, err)
		}
	}()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "镜像拉取已开始，请稍后查看状态",
	})
}

// GetTemplate 获取模板信息
// @Summary 获取模板信息
// @Description 根据ID获取模板详细信息
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Success 200 {object} Response{data=models.SwaggerTemplate}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates/{id} [get]
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	idStr := c.Param("id")

	template, err := h.templateService.GetTemplate(idStr)
	if err != nil {
		if err == services.ErrTemplateNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询模板失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "查询成功",
		Data:    template,
	})
}

// UpdateTemplate 更新模板信息
// @Summary 更新模板信息
// @Description 更新模板的基本信息
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Param template body models.TemplateUpdateRequest true "更新信息"
// @Success 200 {object} Response{data=models.SwaggerTemplate}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates/{id} [put]
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	idStr := c.Param("id")

	var req models.TemplateUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 转换更新请求为map
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}
	if req.ConfigSchema != nil {
		updates["config_schema"] = req.ConfigSchema
	}
	if req.DefaultConfig != nil {
		updates["default_config"] = req.DefaultConfig
	}
	if req.ResourceRequirements != nil {
		updates["resource_requirements"] = req.ResourceRequirements
	}

	err := h.templateService.UpdateTemplate(idStr, updates)
	if err != nil {
		switch err {
		case services.ErrTemplateNotFound:
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "update_failed",
				Message: "更新模板失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "更新成功",
	})
}

// DeleteTemplate 删除模板
// @Summary 删除模板
// @Description 删除指定模板
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "模板ID"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates/{id} [delete]
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")

	err := h.templateService.DeleteTemplate(idStr)
	if err != nil {
		switch err {
		case services.ErrTemplateNotFound:
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
		default:
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "delete_failed",
				Message: "删除模板失败: " + err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "删除成功",
	})
}

// ListTemplates 获取模板列表
// @Summary 获取模板列表
// @Description 分页获取模板列表，支持过滤
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param page query int false "页码" default(1)
// @Param size query int false "每页大小" default(20)
// @Param type query string false "模板类型"
// @Param status query string false "模板状态"
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} PaginatedResponse{data=[]models.SwaggerTemplate}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates [get]
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "20"))

	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 20
	}

	// 构建搜索关键词
	search := c.Query("keyword")

	templates, total, err := h.templateService.GetTemplates(page, size, search)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询模板列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, PaginatedResponse{
		Success: true,
		Message: "查询成功",
		Data:    templates,
		Pagination: PaginationInfo{
			Page:       page,
			Size:       size,
			Total:      total,
			TotalPages: (total + int64(size) - 1) / int64(size),
		},
	})
}

// DeployTemplate 部署模板
// @Summary 部署模板
// @Description 将模板部署到指定节点
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param deployment body models.TemplateDeployRequest true "部署信息"
// @Success 201 {object} Response{data=models.SwaggerServiceDeployment}
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/templates/deploy [post]
func (h *TemplateHandler) DeployTemplate(c *gin.Context) {
	var req models.TemplateDeployRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 简化实现：返回成功响应
	// 在实际实现中，这里应该调用部署服务
	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "部署请求已提交",
		Data: map[string]interface{}{
			"template_id": c.Param("id"),
			"node_ids":    req.NodeIDs,
			"config":      req.Config,
			"status":      "pending",
		},
	})
}
