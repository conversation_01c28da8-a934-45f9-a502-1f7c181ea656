package handlers

import (
	"net/http"
	"time"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// WebHandler Web页面处理器
type WebHandler struct {
	authService      *services.AuthService
	nodeService      *services.NodeService
	templateService  *services.TemplateService
	dashboardService *services.DashboardService
}

// NewWebHandler 创建Web处理器
func NewWebHandler(
	authService *services.AuthService,
	nodeService *services.NodeService,
	templateService *services.TemplateService,
	dashboardService *services.DashboardService,
) *WebHandler {
	return &WebHandler{
		authService:      authService,
		nodeService:      nodeService,
		templateService:  templateService,
		dashboardService: dashboardService,
	}
}

// PageData 页面数据结构
type PageData struct {
	Title          string           `json:"title"`
	ActivePage     string           `json:"active_page"`
	User           *models.UserInfo `json:"user,omitempty"`
	PageHeader     string           `json:"page_header,omitempty"`
	Breadcrumb     []BreadcrumbItem `json:"breadcrumb,omitempty"`
	PageActions    []PageAction     `json:"page_actions,omitempty"`
	Data           interface{}      `json:"data,omitempty"`
	SuccessMessage string           `json:"success_message,omitempty"`
	ErrorMessage   string           `json:"error_message,omitempty"`
	InfoMessage    string           `json:"info_message,omitempty"`
	CurrentTime    string           `json:"current_time"`
}

// BreadcrumbItem 面包屑导航项
type BreadcrumbItem struct {
	Name   string `json:"name"`
	URL    string `json:"url,omitempty"`
	Active bool   `json:"active"`
}

// PageAction 页面操作按钮
type PageAction struct {
	Text  string `json:"text"`
	URL   string `json:"url"`
	Icon  string `json:"icon,omitempty"`
	Class string `json:"class"`
}

// ShowLogin 显示登录页面
func (h *WebHandler) ShowLogin(c *gin.Context) {
	data := PageData{
		Title:       "登录",
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	// 检查是否有错误消息
	if msg := c.Query("error"); msg != "" {
		data.ErrorMessage = msg
	}
	if msg := c.Query("info"); msg != "" {
		data.InfoMessage = msg
	}

	c.HTML(http.StatusOK, "login.html", data)
}

// ShowDashboard 显示仪表板页面
func (h *WebHandler) ShowDashboard(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "仪表板",
		ActivePage: "dashboard",
		User:       user,
		PageHeader: "系统概览",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "仪表板", Active: true},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "dashboard.html", data)
}

// ShowNodes 显示节点列表页面
func (h *WebHandler) ShowNodes(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "节点管理",
		ActivePage: "nodes",
		User:       user,
		PageHeader: "节点管理",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "节点管理", Active: true},
		},
		PageActions: []PageAction{
			{
				Text:  "注册节点",
				URL:   "/nodes/register",
				Icon:  "fas fa-plus",
				Class: "btn btn-primary",
			},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "nodes.html", data)
}

// ShowNodeRegister 显示节点注册页面
func (h *WebHandler) ShowNodeRegister(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "注册节点",
		ActivePage: "nodes",
		User:       user,
		PageHeader: "注册新节点",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "节点管理", URL: "/nodes", Active: false},
			{Name: "注册节点", Active: true},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "node_register.html", data)
}

// ShowTemplates 显示模板列表页面
func (h *WebHandler) ShowTemplates(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "模板管理",
		ActivePage: "templates",
		User:       user,
		PageHeader: "模板管理",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "模板管理", Active: true},
		},
		PageActions: []PageAction{
			{
				Text:  "创建模板",
				URL:   "/templates/create",
				Icon:  "fas fa-plus",
				Class: "btn btn-primary",
			},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "templates.html", data)
}

// ShowTemplateCreate 显示模板创建页面
func (h *WebHandler) ShowTemplateCreate(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "创建模板",
		ActivePage: "templates",
		User:       user,
		PageHeader: "创建新模板",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "模板管理", URL: "/templates", Active: false},
			{Name: "创建模板", Active: true},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "template_create.html", data)
}

// ShowAttacks 显示攻击数据页面
func (h *WebHandler) ShowAttacks(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "攻击数据",
		ActivePage: "attacks",
		User:       user,
		PageHeader: "攻击数据分析",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "攻击数据", Active: true},
		},
		PageActions: []PageAction{
			{
				Text:  "导出数据",
				URL:   "/attacks/export",
				Icon:  "fas fa-download",
				Class: "btn btn-success",
			},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "attacks.html", data)
}

// ShowReports 显示报表页面
func (h *WebHandler) ShowReports(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "报表分析",
		ActivePage: "reports",
		User:       user,
		PageHeader: "报表分析",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "报表分析", Active: true},
		},
		PageActions: []PageAction{
			{
				Text:  "生成报表",
				URL:   "/reports/generate",
				Icon:  "fas fa-chart-bar",
				Class: "btn btn-primary",
			},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "reports.html", data)
}

// ShowUsers 显示用户管理页面（仅管理员）
func (h *WebHandler) ShowUsers(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	// 检查管理员权限
	if user.Role != "administrator" {
		c.HTML(http.StatusForbidden, "error.html", PageData{
			Title:        "访问被拒绝",
			ErrorMessage: "您没有权限访问此页面",
		})
		return
	}

	data := PageData{
		Title:      "用户管理",
		ActivePage: "users",
		User:       user,
		PageHeader: "用户管理",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "用户管理", Active: true},
		},
		PageActions: []PageAction{
			{
				Text:  "创建用户",
				URL:   "/users/create",
				Icon:  "fas fa-user-plus",
				Class: "btn btn-primary",
			},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "users.html", data)
}

// ShowProfile 显示个人资料页面
func (h *WebHandler) ShowProfile(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "个人资料",
		ActivePage: "profile",
		User:       user,
		PageHeader: "个人资料",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "个人资料", Active: true},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "profile.html", data)
}

// ShowRealtime 显示实时监控页面
func (h *WebHandler) ShowRealtime(c *gin.Context) {
	user := h.getCurrentUser(c)
	if user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	data := PageData{
		Title:      "实时监控",
		ActivePage: "realtime",
		User:       user,
		PageHeader: "实时监控",
		Breadcrumb: []BreadcrumbItem{
			{Name: "首页", URL: "/", Active: false},
			{Name: "实时监控", Active: true},
		},
		CurrentTime: time.Now().Format("2006-01-02 15:04:05"),
	}

	c.HTML(http.StatusOK, "realtime.html", data)
}

// HandleLogout 处理登出
func (h *WebHandler) HandleLogout(c *gin.Context) {
	// 清除会话（如果使用服务器端会话）
	// 这里简化处理，实际应该清除服务器端的会话数据
	c.Redirect(http.StatusFound, "/login?info=已成功登出")
}

// 辅助方法

// getCurrentUser 获取当前用户信息
func (h *WebHandler) getCurrentUser(c *gin.Context) *models.UserInfo {
	// 从上下文中获取用户信息（由认证中间件设置）
	if user, exists := c.Get("user"); exists {
		if userModel, ok := user.(*models.User); ok {
			return userModel.ToUserInfo()
		}
	}
	return nil
}

// setFlashMessage 设置闪存消息
func (h *WebHandler) setFlashMessage(c *gin.Context, msgType, message string) {
	// 这里可以实现闪存消息的存储逻辑
	// 简化实现，使用查询参数
	switch msgType {
	case "success":
		c.Redirect(http.StatusFound, c.Request.URL.Path+"?success="+message)
	case "error":
		c.Redirect(http.StatusFound, c.Request.URL.Path+"?error="+message)
	case "info":
		c.Redirect(http.StatusFound, c.Request.URL.Path+"?info="+message)
	}
}
