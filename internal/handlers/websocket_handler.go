package handlers

import (
	"net/http"
	"strconv"
	"time"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"
	"honeypot-admin/internal/websocket"

	"github.com/gin-gonic/gin"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	wsManager       *websocket.WebSocketManager
	nodeService     *services.NodeService
	templateService *services.TemplateService
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(
	wsManager *websocket.WebSocketManager,
	nodeService *services.NodeService,
	templateService *services.TemplateService,
) *WebSocketHandler {
	return &WebSocketHandler{
		wsManager:       wsManager,
		nodeService:     nodeService,
		templateService: templateService,
	}
}

// HandleNodeConnection 处理节点WebSocket连接
// @Summary 节点WebSocket连接
// @Description 节点通过WebSocket连接到管理平台
// @Tags WebSocket
// @Accept json
// @Produce json
// @Router /ws/node [get]
func (h *WebSocketHandler) HandleNodeConnection(c *gin.Context) {
	h.wsManager.HandleNodeWebSocket(c)
}

// HandleAdminConnection 处理管理员WebSocket连接
// @Summary 管理员WebSocket连接
// @Description 管理员通过WebSocket连接到管理平台
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Router /ws/admin [get]
func (h *WebSocketHandler) HandleAdminConnection(c *gin.Context) {
	h.wsManager.HandleAdminWebSocket(c)
}

// SendNodeCommand 发送节点命令
// @Summary 发送节点命令
// @Description 向指定节点发送控制命令
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param node_id path string true "节点ID"
// @Param command body models.NodeCommandRequest true "命令信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{node_id}/command [post]
func (h *WebSocketHandler) SendNodeCommand(c *gin.Context) {
	nodeID := c.Param("node_id")
	if nodeID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_node_id",
			Message: "节点ID不能为空",
		})
		return
	}

	var req models.NodeCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 验证节点是否存在
	_, err := h.nodeService.GetNode(nodeID)
	if err != nil {
		if err == services.ErrNodeNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "node_not_found",
				Message: "节点不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询节点失败: " + err.Error(),
		})
		return
	}

	// 创建命令消息
	message := websocket.Message{
		Type:      websocket.MessageTypeNodeCommand,
		From:      "admin",
		To:        nodeID,
		Data:      req,
		Timestamp: time.Now(),
		RequestID: generateRequestID(),
	}

	// 发送命令到节点
	if err := h.wsManager.SendToNode(nodeID, message); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "send_failed",
			Message: "发送命令失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "命令发送成功",
		Data: map[string]interface{}{
			"node_id":    nodeID,
			"command":    req.Command,
			"request_id": message.RequestID,
		},
	})
}

// SendDeployCommand 发送部署命令
// @Summary 发送部署命令
// @Description 向指定节点发送服务部署命令
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param node_id path string true "节点ID"
// @Param command body models.DeployCommandRequest true "部署命令信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/nodes/{node_id}/deploy [post]
func (h *WebSocketHandler) SendDeployCommand(c *gin.Context) {
	nodeID := c.Param("node_id")
	if nodeID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_node_id",
			Message: "节点ID不能为空",
		})
		return
	}

	var req models.DeployCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 验证模板是否存在
	template, err := h.templateService.GetTemplate(req.TemplateID)
	if err != nil {
		if err == services.ErrTemplateNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error:   "template_not_found",
				Message: "模板不存在",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "query_failed",
			Message: "查询模板失败: " + err.Error(),
		})
		return
	}

	// 创建部署命令消息
	message := websocket.Message{
		Type: websocket.MessageTypeDeployCommand,
		From: "admin",
		To:   nodeID,
		Data: map[string]interface{}{
			"template_id":   req.TemplateID,
			"template_name": template.Name,
			"image_name":    template.ImageName,
			"config":        req.Config,
			"action":        req.Action,
		},
		Timestamp: time.Now(),
		RequestID: generateRequestID(),
	}

	// 发送部署命令到节点
	if err := h.wsManager.SendToNode(nodeID, message); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "send_failed",
			Message: "发送部署命令失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "部署命令发送成功",
		Data: map[string]interface{}{
			"node_id":     nodeID,
			"template_id": req.TemplateID,
			"action":      req.Action,
			"request_id":  message.RequestID,
		},
	})
}

// GetConnectionStatus 获取连接状态
// @Summary 获取WebSocket连接状态
// @Description 获取当前WebSocket连接的统计信息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} Response
// @Failure 401 {object} ErrorResponse
// @Router /api/v1/websocket/status [get]
func (h *WebSocketHandler) GetConnectionStatus(c *gin.Context) {
	stats := h.wsManager.GetConnectionStats()
	connectedNodes := h.wsManager.GetConnectedNodes()
	connectedAdmins := h.wsManager.GetConnectedAdmins()

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "获取连接状态成功",
		Data: map[string]interface{}{
			"statistics":       stats,
			"connected_nodes":  connectedNodes,
			"connected_admins": connectedAdmins,
			"timestamp":        time.Now(),
		},
	})
}

// BroadcastNotification 广播通知
// @Summary 广播通知
// @Description 向所有管理员广播通知消息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param notification body models.NotificationRequest true "通知信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/websocket/broadcast [post]
func (h *WebSocketHandler) BroadcastNotification(c *gin.Context) {
	var req models.NotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 获取当前用户信息
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "未授权访问",
		})
		return
	}

	userModel := user.(*models.User)

	// 创建通知消息
	message := websocket.Message{
		Type: websocket.MessageTypeNotification,
		From: userModel.Username,
		Data: map[string]interface{}{
			"title":     req.Title,
			"message":   req.Message,
			"type":      req.Type,
			"sender":    userModel.Username,
			"timestamp": time.Now(),
		},
		Timestamp: time.Now(),
	}

	// 广播通知
	h.wsManager.BroadcastToAdmins(message)

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "通知广播成功",
		Data: map[string]interface{}{
			"title":   req.Title,
			"message": req.Message,
			"type":    req.Type,
		},
	})
}

// SendToAdmin 发送消息给指定管理员
// @Summary 发送消息给管理员
// @Description 向指定管理员发送消息
// @Tags WebSocket
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param user_id path string true "用户ID"
// @Param message body models.AdminMessageRequest true "消息信息"
// @Success 200 {object} Response
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/v1/websocket/admin/{user_id}/message [post]
func (h *WebSocketHandler) SendToAdmin(c *gin.Context) {
	userIDStr := c.Param("user_id")
	if userIDStr == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_user_id",
			Message: "用户ID不能为空",
		})
		return
	}

	var req models.AdminMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "invalid_request",
			Message: "请求参数无效: " + err.Error(),
		})
		return
	}

	// 获取当前用户信息
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "unauthorized",
			Message: "未授权访问",
		})
		return
	}

	userModel := user.(*models.User)

	// 创建消息
	message := websocket.Message{
		Type: websocket.MessageTypeNotification,
		From: userModel.Username,
		To:   "admin_" + userIDStr,
		Data: map[string]interface{}{
			"message":   req.Message,
			"type":      req.Type,
			"sender":    userModel.Username,
			"timestamp": time.Now(),
		},
		Timestamp: time.Now(),
	}

	// 发送消息
	if err := h.wsManager.SendToAdmin("admin_"+userIDStr, message); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "send_failed",
			Message: "发送消息失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, Response{
		Success: true,
		Message: "消息发送成功",
		Data: map[string]interface{}{
			"user_id": userIDStr,
			"message": req.Message,
			"type":    req.Type,
		},
	})
}

// 辅助函数

// generateRequestID 生成请求ID
func generateRequestID() string {
	return strconv.FormatInt(time.Now().UnixNano(), 36)
}
