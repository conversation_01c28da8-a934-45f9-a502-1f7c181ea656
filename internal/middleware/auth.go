package middleware

import (
	"net/http"
	"strings"

	"honeypot-admin/internal/handlers"
	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Header获取Token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "missing_token",
				Message: "缺少认证Token",
			})
			c.Abort()
			return
		}

		// 检查Token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "invalid_token_format",
				Message: "Token格式无效",
			})
			c.Abort()
			return
		}

		token := parts[1]

		// 验证Token
		user, err := authService.ValidateToken(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "invalid_token",
				Message: "Token无效或已过期",
			})
			c.Abort()
			return
		}

		// 检查用户状态
		if !user.IsActive() {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "user_inactive",
				Message: "用户账户已被禁用",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("user", user)
		c.Set("user_id", user.ID)
		c.Set("username", user.Username)
		c.Set("user_role", user.Role)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func RequireRole(requiredRoles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "unauthorized",
				Message: "未授权访问",
			})
			c.Abort()
			return
		}

		userObj := user.(*models.User)

		// 检查用户角色
		hasPermission := false
		for _, role := range requiredRoles {
			if userObj.Role == role {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, handlers.ErrorResponse{
				Success: false,
				Error:   "insufficient_permissions",
				Message: "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission 权限检查中间件
func RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "unauthorized",
				Message: "未授权访问",
			})
			c.Abort()
			return
		}

		userObj := user.(*models.User)

		// 检查用户权限
		if !userObj.HasPermission(permission) {
			c.JSON(http.StatusForbidden, handlers.ErrorResponse{
				Success: false,
				Error:   "insufficient_permissions",
				Message: "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminOnly 仅管理员访问中间件
func AdminOnly() gin.HandlerFunc {
	return RequireRole(models.RoleAdministrator)
}

// OperatorOrAdmin 操作员或管理员访问中间件
func OperatorOrAdmin() gin.HandlerFunc {
	return RequireRole(models.RoleAdministrator, models.RoleOperator)
}

// SelfOrAdmin 自己或管理员访问中间件（用于用户资源访问）
func SelfOrAdmin() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, handlers.ErrorResponse{
				Success: false,
				Error:   "unauthorized",
				Message: "未授权访问",
			})
			c.Abort()
			return
		}

		userObj := user.(*models.User)

		// 如果是管理员，直接通过
		if userObj.Role == models.RoleAdministrator {
			c.Next()
			return
		}

		// 检查是否访问自己的资源
		resourceUserID := c.Param("id")
		if resourceUserID == "" {
			// 如果没有指定用户ID，允许访问（通常是获取自己的信息）
			c.Next()
			return
		}

		// 检查是否是自己的ID
		if resourceUserID == string(rune(userObj.ID)) {
			c.Next()
			return
		}

		c.JSON(http.StatusForbidden, handlers.ErrorResponse{
			Success: false,
			Error:   "access_denied",
			Message: "只能访问自己的资源",
		})
		c.Abort()
	}
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func OptionalAuth(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.Next()
			return
		}

		token := parts[1]
		user, err := authService.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		if user.IsActive() {
			c.Set("user", user)
			c.Set("user_id", user.ID)
			c.Set("username", user.Username)
			c.Set("user_role", user.Role)
		}

		c.Next()
	}
}
