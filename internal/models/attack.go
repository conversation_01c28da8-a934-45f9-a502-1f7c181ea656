package models

import (
	"time"

	"gorm.io/gorm"
)

// AttackEvent 攻击事件
type AttackEvent struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	NodeID      string         `json:"node_id" gorm:"index"`
	SourceIP    string         `json:"source_ip" gorm:"index"`
	SourcePort  int            `json:"source_port"`
	TargetIP    string         `json:"target_ip"`
	TargetPort  int            `json:"target_port"`
	Protocol    string         `json:"protocol"`
	AttackType  string         `json:"attack_type" gorm:"index"`
	Severity    string         `json:"severity" gorm:"index"`
	Payload     string         `json:"payload" gorm:"type:text"`
	Headers     string         `json:"headers" gorm:"type:text"`
	UserAgent   string         `json:"user_agent"`
	Method      string         `json:"method"`
	URI         string         `json:"uri"`
	Description string         `json:"description"`
	RawData     string         `json:"raw_data" gorm:"type:text"`
	Country     string         `json:"country"`
	Region      string         `json:"region"`
	City        string         `json:"city"`
	ISP         string         `json:"isp"`
	Blocked     bool           `json:"blocked" gorm:"default:false"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// AttackStatistics 攻击统计
type AttackStatistics struct {
	TotalAttacks      int64                    `json:"total_attacks"`
	TodayAttacks      int64                    `json:"today_attacks"`
	UniqueIPs         int64                    `json:"unique_ips"`
	TopAttackTypes    []AttackTypeDistribution `json:"top_attack_types"`
	TopSourceIPs      []SourceIPCount          `json:"top_source_ips"`
	AttacksByHour     []HourlyAttackStat       `json:"attacks_by_hour"`
	AttacksBySeverity []SeverityCount          `json:"attacks_by_severity"`
	GeographicData    []GeographicDistribution `json:"geographic_data"`
}

// HourlyAttackStat 每小时攻击统计
type HourlyAttackStat struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}

// SeverityCount 严重程度计数
type SeverityCount struct {
	Severity string `json:"severity"`
	Count    int64  `json:"count"`
}

// AttackTrend 攻击趋势
type AttackTrend struct {
	Date     string `json:"date"`
	Count    int64  `json:"count"`
	Type     string `json:"type"`
	Severity string `json:"severity"`
}

// AttackPattern 攻击模式
type AttackPattern struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex"`
	Type        string         `json:"type"`
	Pattern     string         `json:"pattern"`
	Description string         `json:"description"`
	Severity    string         `json:"severity"`
	Active      bool           `json:"active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// AttackRule 攻击规则
type AttackRule struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex"`
	Description string         `json:"description"`
	Conditions  string         `json:"conditions" gorm:"type:text"`
	Actions     string         `json:"actions" gorm:"type:text"`
	Priority    int            `json:"priority" gorm:"default:1"`
	Active      bool           `json:"active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// AttackSession 攻击会话
type AttackSession struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	SessionID   string         `json:"session_id" gorm:"uniqueIndex"`
	SourceIP    string         `json:"source_ip" gorm:"index"`
	NodeID      string         `json:"node_id" gorm:"index"`
	StartTime   time.Time      `json:"start_time"`
	EndTime     *time.Time     `json:"end_time"`
	Duration    int64          `json:"duration"` // 秒
	EventCount  int            `json:"event_count"`
	AttackTypes string         `json:"attack_types"`
	Severity    string         `json:"severity"`
	Status      string         `json:"status"` // active, completed, blocked
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// BlacklistedIP 黑名单IP
type BlacklistedIP struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	IP          string         `json:"ip" gorm:"uniqueIndex"`
	Reason      string         `json:"reason"`
	AddedBy     string         `json:"added_by"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	AttackCount int64          `json:"attack_count"`
	LastSeen    time.Time      `json:"last_seen"`
	Active      bool           `json:"active" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// AttackReport 攻击报告
type AttackReport struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Period      string         `json:"period"` // daily, weekly, monthly
	StartDate   time.Time      `json:"start_date"`
	EndDate     time.Time      `json:"end_date"`
	Data        string         `json:"data" gorm:"type:text"`
	Format      string         `json:"format"` // json, pdf, csv
	Status      string         `json:"status"` // pending, completed, failed
	FilePath    string         `json:"file_path"`
	GeneratedBy string         `json:"generated_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// TargetPortCount 目标端口计数
type TargetPortCount struct {
	TargetPort int   `json:"target_port"`
	Count      int64 `json:"count"`
}

// HourlyAttackCount 每小时攻击计数
type HourlyAttackCount struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}
