package models

import "time"

// DashboardOverview 仪表板概览
type DashboardOverview struct {
	NodeStats       NodeStats       `json:"node_stats"`
	AttackStats     AttackStats     `json:"attack_stats"`
	TemplateStats   TemplateStats   `json:"template_stats"`
	DeploymentStats DeploymentStats `json:"deployment_stats"`
	SystemStatus    SystemStatus    `json:"system_status"`
}

// NodeStats 节点统计
type NodeStats struct {
	Total    int `json:"total"`
	Online   int `json:"online"`
	Offline  int `json:"offline"`
	Warning  int `json:"warning"`
	Critical int `json:"critical"`
	Error    int `json:"error"`
}

// AttackStats 攻击统计
type AttackStats struct {
	TotalAttacks   int64 `json:"total_attacks"`
	TodayAttacks   int64 `json:"today_attacks"`
	UniqueIPs      int64 `json:"unique_ips"`
	BlockedAttacks int64 `json:"blocked_attacks"`
}

// TemplateStats 模板统计
type TemplateStats struct {
	Total    int `json:"total"`
	Active   int `json:"active"`
	Inactive int `json:"inactive"`
	Deployed int `json:"deployed"`
}

// DeploymentStats 部署统计
type DeploymentStats struct {
	Total   int `json:"total"`
	Running int `json:"running"`
	Stopped int `json:"stopped"`
	Failed  int `json:"failed"`
}

// SystemStatus 系统状态
type SystemStatus struct {
	Status      string    `json:"status"`
	Uptime      string    `json:"uptime"`
	Version     string    `json:"version"`
	CPUUsage    float64   `json:"cpu_usage"`
	MemoryUsage float64   `json:"memory_usage"`
	DiskUsage   float64   `json:"disk_usage"`
	Issues      []string  `json:"issues"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// AttackTrendsResponse 攻击趋势响应
type AttackTrendsResponse struct {
	Labels         []string          `json:"labels"`
	Data           []AttackTrend     `json:"data"`
	Period         string            `json:"period"`
	DailyStats     []DailyAttackStat `json:"daily_stats"`
	TopAttackTypes []AttackTypeCount `json:"top_attack_types"`
	TopSourceIPs   []SourceIPCount   `json:"top_source_ips"`
}

// NodeStatusOverview 节点状态概览
type NodeStatusOverview struct {
	NodeID            string             `json:"node_id"`
	NodeName          string             `json:"node_name"`
	Status            string             `json:"status"`
	LastSeen          time.Time          `json:"last_seen"`
	AttackCount       int64              `json:"attack_count"`
	CPUUsage          float64            `json:"cpu_usage"`
	MemoryUsage       float64            `json:"memory_usage"`
	Region            string             `json:"region"`
	StatusStats       []NodeStatusStat   `json:"status_stats"`
	RegionStats       []NodeRegionStat   `json:"region_stats"`
	RecentActiveNodes []RecentActiveNode `json:"recent_active_nodes"`
}

// SecurityAlertsResponse 安全警报响应
type SecurityAlertsResponse struct {
	Alerts          []SecurityAlert       `json:"alerts"`
	Total           int                   `json:"total"`
	CriticalAttacks []CriticalAttackAlert `json:"critical_attacks"`
	SuspiciousIPs   []SuspiciousIPAlert   `json:"suspicious_ips"`
	OfflineNodes    []OfflineNodeAlert    `json:"offline_nodes"`
	GeneratedAt     time.Time             `json:"generated_at"`
}

// SecurityAlert 安全警报
type SecurityAlert struct {
	ID          uint      `json:"id"`
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	NodeID      string    `json:"node_id"`
	NodeName    string    `json:"node_name"`
	SourceIP    string    `json:"source_ip"`
	CreatedAt   time.Time `json:"created_at"`
	Status      string    `json:"status"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	DatabaseStats   DatabaseStats   `json:"database_stats"`
	StorageStats    StorageStats    `json:"storage_stats"`
	ProcessingStats ProcessingStats `json:"processing_stats"`
	NetworkStats    NetworkStats    `json:"network_stats"`
}

// DatabaseStats 数据库统计
type DatabaseStats struct {
	Connections   int     `json:"connections"`
	QueriesPerSec float64 `json:"queries_per_sec"`
	SlowQueries   int     `json:"slow_queries"`
	DatabaseSize  int64   `json:"database_size"`
	TableCount    int     `json:"table_count"`
	IndexSize     int64   `json:"index_size"`
}

// StorageStats 存储统计
type StorageStats struct {
	TotalSpace   int64   `json:"total_space"`
	UsedSpace    int64   `json:"used_space"`
	FreeSpace    int64   `json:"free_space"`
	UsagePercent float64 `json:"usage_percent"`
	LogSize      int64   `json:"log_size"`
	BackupSize   int64   `json:"backup_size"`
	TotalSize    int64   `json:"total_size"`
	DataSize     int64   `json:"data_size"`
	IndexSize    int64   `json:"index_size"`
}

// ProcessingStats 处理统计
type ProcessingStats struct {
	EventsPerSec       float64 `json:"events_per_sec"`
	ProcessingDelay    float64 `json:"processing_delay"`
	QueueSize          int     `json:"queue_size"`
	ErrorRate          float64 `json:"error_rate"`
	Throughput         float64 `json:"throughput"`
	AttacksLastHour    int64   `json:"attacks_last_hour"`
	AttacksLast24Hours int64   `json:"attacks_last_24_hours"`
	AttacksPerHour     float64 `json:"attacks_per_hour"`
	AttacksPerMinute   float64 `json:"attacks_per_minute"`
}

// NetworkStats 网络统计
type NetworkStats struct {
	BytesIn     int64   `json:"bytes_in"`
	BytesOut    int64   `json:"bytes_out"`
	PacketsIn   int64   `json:"packets_in"`
	PacketsOut  int64   `json:"packets_out"`
	Connections int     `json:"connections"`
	Bandwidth   float64 `json:"bandwidth"`
}

// ChartData 图表数据
type ChartData struct {
	Labels   []string  `json:"labels"`
	Datasets []Dataset `json:"datasets"`
}

// Dataset 数据集
type Dataset struct {
	Label           string    `json:"label"`
	Data            []float64 `json:"data"`
	BackgroundColor string    `json:"backgroundColor"`
	BorderColor     string    `json:"borderColor"`
	Fill            bool      `json:"fill"`
}

// TimeSeriesData 时间序列数据
type TimeSeriesData struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
	Label     string    `json:"label"`
}

// AttackTypeDistribution 攻击类型分布
type AttackTypeDistribution struct {
	Type       string  `json:"type"`
	Count      int64   `json:"count"`
	Percentage float64 `json:"percentage"`
}

// GeographicDistribution 地理分布
type GeographicDistribution struct {
	Country   string  `json:"country"`
	Count     int64   `json:"count"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// TopAttackers 顶级攻击者
type TopAttackers struct {
	IP       string    `json:"ip"`
	Count    int64     `json:"count"`
	Country  string    `json:"country"`
	LastSeen time.Time `json:"last_seen"`
}

// SystemHealth 系统健康状态
type SystemHealth struct {
	Overall    string            `json:"overall"`
	Components []ComponentHealth `json:"components"`
	Metrics    HealthMetrics     `json:"metrics"`
	UpdatedAt  time.Time         `json:"updated_at"`
}

// ComponentHealth 组件健康状态
type ComponentHealth struct {
	Name    string `json:"name"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// HealthMetrics 健康指标
type HealthMetrics struct {
	ResponseTime float64 `json:"response_time"`
	Availability float64 `json:"availability"`
	ErrorRate    float64 `json:"error_rate"`
	Throughput   float64 `json:"throughput"`
}

// DailyAttackStat 每日攻击统计
type DailyAttackStat struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// AttackTypeCount 攻击类型计数
type AttackTypeCount struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

// SourceIPCount 源IP计数
type SourceIPCount struct {
	IP    string `json:"ip"`
	Count int64  `json:"count"`
}

// NodeStatusStat 节点状态统计
type NodeStatusStat struct {
	Status string `json:"status"`
	Count  int    `json:"count"`
}

// NodeRegionStat 节点区域统计
type NodeRegionStat struct {
	Region string `json:"region"`
	Count  int    `json:"count"`
}

// RecentActiveNode 最近活跃节点
type RecentActiveNode struct {
	NodeID   string    `json:"node_id"`
	NodeName string    `json:"node_name"`
	LastSeen time.Time `json:"last_seen"`
	Status   string    `json:"status"`
}

// CriticalAttackAlert 严重攻击警报
type CriticalAttackAlert struct {
	ID          uint      `json:"id"`
	AttackType  string    `json:"attack_type"`
	SourceIP    string    `json:"source_ip"`
	TargetNode  string    `json:"target_node"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

// SuspiciousIPAlert 可疑IP警报
type SuspiciousIPAlert struct {
	ID          uint      `json:"id"`
	IP          string    `json:"ip"`
	AttackCount int64     `json:"attack_count"`
	LastSeen    time.Time `json:"last_seen"`
	Country     string    `json:"country"`
	Reason      string    `json:"reason"`
}

// OfflineNodeAlert 离线节点警报
type OfflineNodeAlert struct {
	ID         uint      `json:"id"`
	NodeID     string    `json:"node_id"`
	NodeName   string    `json:"node_name"`
	LastSeen   time.Time `json:"last_seen"`
	OfflineFor string    `json:"offline_for"`
}
