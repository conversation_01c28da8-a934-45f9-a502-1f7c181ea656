package models

import (
	"time"

	"gorm.io/gorm"
)

// ServiceDeployment 服务部署
type ServiceDeployment struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null"`
	NodeID      string         `json:"node_id" gorm:"index;not null"`
	TemplateID  uint           `json:"template_id" gorm:"index;not null"`
	ContainerID string         `json:"container_id" gorm:"uniqueIndex"`
	ImageName   string         `json:"image_name" gorm:"not null"`
	Status      string         `json:"status" gorm:"index;default:'pending'"` // pending, running, stopped, failed, error
	Config      string         `json:"config" gorm:"type:text"`
	Ports       string         `json:"ports" gorm:"type:text"`
	Environment string         `json:"environment" gorm:"type:text"`
	Volumes     string         `json:"volumes" gorm:"type:text"`
	Networks    string         `json:"networks" gorm:"type:text"`
	StartedAt   *time.Time     `json:"started_at"`
	StoppedAt   *time.Time     `json:"stopped_at"`
	Error       string         `json:"error" gorm:"type:text"`
	Logs        string         `json:"logs" gorm:"type:text"`
	CreatedBy   string         `json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Node     Node     `json:"node" gorm:"foreignKey:NodeID;references:ID"`
	Template Template `json:"template" gorm:"foreignKey:TemplateID"`
}

// DeploymentLog 部署日志
type DeploymentLog struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	DeploymentID uint           `json:"deployment_id" gorm:"index;not null"`
	Level        string         `json:"level" gorm:"index"` // info, warning, error, debug
	Message      string         `json:"message" gorm:"type:text;not null"`
	Source       string         `json:"source"` // container, system, user
	Timestamp    time.Time      `json:"timestamp" gorm:"index"`
	CreatedAt    time.Time      `json:"created_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Deployment ServiceDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// DeploymentMetrics 部署指标
type DeploymentMetrics struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	DeploymentID uint           `json:"deployment_id" gorm:"index;not null"`
	CPUUsage     float64        `json:"cpu_usage"`
	MemoryUsage  float64        `json:"memory_usage"`
	MemoryLimit  int64          `json:"memory_limit"`
	NetworkRx    int64          `json:"network_rx"`
	NetworkTx    int64          `json:"network_tx"`
	DiskRead     int64          `json:"disk_read"`
	DiskWrite    int64          `json:"disk_write"`
	Timestamp    time.Time      `json:"timestamp" gorm:"index"`
	CreatedAt    time.Time      `json:"created_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Deployment ServiceDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// DeploymentCommand 部署命令
type DeploymentCommand struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	DeploymentID uint           `json:"deployment_id" gorm:"index;not null"`
	Command      string         `json:"command" gorm:"not null"` // start, stop, restart, update, delete
	Parameters   string         `json:"parameters" gorm:"type:text"`
	Status       string         `json:"status" gorm:"index;default:'pending'"` // pending, executing, completed, failed
	Result       string         `json:"result" gorm:"type:text"`
	Error        string         `json:"error" gorm:"type:text"`
	ExecutedBy   string         `json:"executed_by"`
	ExecutedAt   *time.Time     `json:"executed_at"`
	CompletedAt  *time.Time     `json:"completed_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Deployment ServiceDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// DeploymentHealth 部署健康检查
type DeploymentHealth struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	DeploymentID uint           `json:"deployment_id" gorm:"index;not null"`
	CheckType    string         `json:"check_type"`    // http, tcp, exec
	Endpoint     string         `json:"endpoint"`      // URL或端口
	Status       string         `json:"status"`        // healthy, unhealthy, unknown
	ResponseTime int64          `json:"response_time"` // 毫秒
	StatusCode   int            `json:"status_code"`
	Message      string         `json:"message"`
	CheckedAt    time.Time      `json:"checked_at" gorm:"index"`
	CreatedAt    time.Time      `json:"created_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Deployment ServiceDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// DeploymentBackup 部署备份
type DeploymentBackup struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	DeploymentID uint           `json:"deployment_id" gorm:"index;not null"`
	BackupType   string         `json:"backup_type"` // config, data, full
	FilePath     string         `json:"file_path"`   // 备份文件路径
	FileSize     int64          `json:"file_size"`   // 文件大小（字节）
	Checksum     string         `json:"checksum"`    // 文件校验和
	Status       string         `json:"status"`      // creating, completed, failed
	Description  string         `json:"description"`
	CreatedBy    string         `json:"created_by"`
	CreatedAt    time.Time      `json:"created_at"`
	CompletedAt  *time.Time     `json:"completed_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// 关联
	Deployment ServiceDeployment `json:"deployment" gorm:"foreignKey:DeploymentID"`
}

// TableName 指定表名
func (ServiceDeployment) TableName() string {
	return "service_deployments"
}

func (DeploymentLog) TableName() string {
	return "deployment_logs"
}

func (DeploymentMetrics) TableName() string {
	return "deployment_metrics"
}

func (DeploymentCommand) TableName() string {
	return "deployment_commands"
}

func (DeploymentHealth) TableName() string {
	return "deployment_health"
}

func (DeploymentBackup) TableName() string {
	return "deployment_backups"
}

// IsRunning 检查部署是否正在运行
func (d *ServiceDeployment) IsRunning() bool {
	return d.Status == "running"
}

// IsStopped 检查部署是否已停止
func (d *ServiceDeployment) IsStopped() bool {
	return d.Status == "stopped"
}

// IsFailed 检查部署是否失败
func (d *ServiceDeployment) IsFailed() bool {
	return d.Status == "failed" || d.Status == "error"
}

// GetUptime 获取运行时间
func (d *ServiceDeployment) GetUptime() time.Duration {
	if d.StartedAt == nil {
		return 0
	}
	if d.IsStopped() && d.StoppedAt != nil {
		return d.StoppedAt.Sub(*d.StartedAt)
	}
	return time.Since(*d.StartedAt)
}

// GetStatusColor 获取状态颜色
func (d *ServiceDeployment) GetStatusColor() string {
	switch d.Status {
	case "running":
		return "success"
	case "stopped":
		return "warning"
	case "failed", "error":
		return "danger"
	case "pending":
		return "info"
	default:
		return "secondary"
	}
}

// CanStart 检查是否可以启动
func (d *ServiceDeployment) CanStart() bool {
	return d.Status == "stopped" || d.Status == "failed"
}

// CanStop 检查是否可以停止
func (d *ServiceDeployment) CanStop() bool {
	return d.Status == "running"
}

// CanRestart 检查是否可以重启
func (d *ServiceDeployment) CanRestart() bool {
	return d.Status == "running" || d.Status == "stopped"
}

// CanDelete 检查是否可以删除
func (d *ServiceDeployment) CanDelete() bool {
	return d.Status != "running"
}

// DeploymentListRequest 部署列表请求
type DeploymentListRequest struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	Size       int    `form:"size,default=20" binding:"min=1,max=100"`
	NodeID     string `form:"node_id"`
	TemplateID uint   `form:"template_id"`
	Status     string `form:"status"`
	Keyword    string `form:"keyword" binding:"omitempty,max=100"`
}

// DeploymentStatistics 部署统计信息
type DeploymentStatistics struct {
	Total   int64 `json:"total"`
	Running int64 `json:"running"`
	Stopped int64 `json:"stopped"`
	Failed  int64 `json:"failed"`
}

// DeploymentCreateRequest 创建部署请求
type DeploymentCreateRequest struct {
	Name        string                 `json:"name" binding:"required,max=100"`
	NodeID      string                 `json:"node_id" binding:"required"`
	TemplateID  uint                   `json:"template_id" binding:"required"`
	Config      map[string]interface{} `json:"config"`
	Ports       []string               `json:"ports"`
	Environment map[string]string      `json:"environment"`
	Volumes     []string               `json:"volumes"`
}

// DeploymentUpdateRequest 更新部署请求
type DeploymentUpdateRequest struct {
	Name        string                 `json:"name" binding:"omitempty,max=100"`
	Config      map[string]interface{} `json:"config"`
	Ports       []string               `json:"ports"`
	Environment map[string]string      `json:"environment"`
	Volumes     []string               `json:"volumes"`
}
