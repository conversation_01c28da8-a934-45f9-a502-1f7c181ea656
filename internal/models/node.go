package models

import (
	"time"

	"gorm.io/gorm"
)

// NodeStatus 节点状态枚举
type NodeStatus string

const (
	NodeStatusOnline  NodeStatus = "online"  // 在线
	NodeStatusOffline NodeStatus = "offline" // 离线
	NodeStatusError   NodeStatus = "error"   // 错误
)

// Node 节点模型
type Node struct {
	ID              string     `gorm:"type:varchar(50);primaryKey" json:"id"`
	Name            string     `gorm:"type:varchar(100);not null" json:"name"`
	IP              string     `gorm:"type:varchar(45);not null" json:"ip"`
	Token           string     `gorm:"type:varchar(255);uniqueIndex;not null" json:"-"` // 不在JSON中返回Token
	Status          NodeStatus `gorm:"type:varchar(20);default:'offline'" json:"status"`
	Region          string     `gorm:"type:varchar(50);default:'default'" json:"region"`
	Description     string     `gorm:"type:text" json:"description"`
	LastHeartbeatAt *time.Time `json:"last_heartbeat_at"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`

	// 关联关系
	Services []ServiceDeployment `gorm:"foreignKey:NodeID;constraint:OnDelete:CASCADE" json:"services,omitempty"`
}

// TableName 指定表名
func (Node) TableName() string {
	return "nodes"
}

// BeforeCreate GORM钩子 - 创建前
func (n *Node) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加创建前的逻辑
	return nil
}

// IsOnline 检查节点是否在线
func (n *Node) IsOnline() bool {
	return n.Status == NodeStatusOnline
}

// GetStatusDisplayName 获取状态显示名称
func (n *Node) GetStatusDisplayName() string {
	switch n.Status {
	case NodeStatusOnline:
		return "在线"
	case NodeStatusOffline:
		return "离线"
	case NodeStatusError:
		return "错误"
	default:
		return "未知"
	}
}

// IsHeartbeatTimeout 检查心跳是否超时
func (n *Node) IsHeartbeatTimeout(timeoutDuration time.Duration) bool {
	if n.LastHeartbeatAt == nil {
		return true
	}
	return time.Since(*n.LastHeartbeatAt) > timeoutDuration
}

// NodeCreateRequest 节点创建请求结构
type NodeCreateRequest struct {
	ID          string `json:"id" binding:"required,min=3,max=50"`
	Name        string `json:"name" binding:"required,min=1,max=100"`
	IP          string `json:"ip" binding:"required,ip"`
	Region      string `json:"region" binding:"omitempty,max=50"`
	Description string `json:"description" binding:"omitempty,max=500"`
}

// NodeUpdateRequest 节点更新请求结构
type NodeUpdateRequest struct {
	Name        string `json:"name" binding:"omitempty,min=1,max=100"`
	IP          string `json:"ip" binding:"omitempty,ip"`
	Region      string `json:"region" binding:"omitempty,max=50"`
	Description string `json:"description" binding:"omitempty,max=500"`
}

// NodeResponse 节点响应结构
type NodeResponse struct {
	ID              string                  `json:"id"`
	Name            string                  `json:"name"`
	IP              string                  `json:"ip"`
	Status          NodeStatus              `json:"status"`
	StatusName      string                  `json:"status_name"`
	Region          string                  `json:"region"`
	Description     string                  `json:"description"`
	LastHeartbeatAt *time.Time              `json:"last_heartbeat_at"`
	CreatedAt       time.Time               `json:"created_at"`
	UpdatedAt       time.Time               `json:"updated_at"`
	Services        []ServiceDeploymentInfo `json:"services,omitempty"`
	ResourceUsage   *NodeResourceUsage      `json:"resource_usage,omitempty"`
}

// NodeResourceUsage 节点资源使用情况
type NodeResourceUsage struct {
	CPUUsage    float64   `json:"cpu_usage"`
	MemoryUsage float64   `json:"memory_usage"`
	DiskUsage   float64   `json:"disk_usage"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ServiceDeploymentInfo 服务部署信息（简化版）
type ServiceDeploymentInfo struct {
	ID          uint              `json:"id"`
	ServiceName string            `json:"service_name"`
	TemplateID  uint              `json:"template_id"`
	Status      string            `json:"status"`
	StatusName  string            `json:"status_name"`
	Ports       map[string]string `json:"ports"`
	DeployedAt  *time.Time        `json:"deployed_at"`
}

// ToNodeResponse 转换为节点响应结构
func (n *Node) ToNodeResponse() *NodeResponse {
	resp := &NodeResponse{
		ID:              n.ID,
		Name:            n.Name,
		IP:              n.IP,
		Status:          n.Status,
		StatusName:      n.GetStatusDisplayName(),
		Region:          n.Region,
		Description:     n.Description,
		LastHeartbeatAt: n.LastHeartbeatAt,
		CreatedAt:       n.CreatedAt,
		UpdatedAt:       n.UpdatedAt,
	}

	// 转换服务信息
	if len(n.Services) > 0 {
		resp.Services = make([]ServiceDeploymentInfo, len(n.Services))
		for i, service := range n.Services {
			resp.Services[i] = ServiceDeploymentInfo{
				ID:          service.ID,
				ServiceName: service.Name,
				TemplateID:  service.TemplateID,
				Status:      service.Status,
				StatusName:  service.GetStatusColor(),
				DeployedAt:  service.StartedAt,
			}
		}
	}

	return resp
}

// NodeListRequest 节点列表请求结构
type NodeListRequest struct {
	Page    int        `form:"page,default=1" binding:"min=1"`
	Size    int        `form:"size,default=20" binding:"min=1,max=100"`
	Status  NodeStatus `form:"status" binding:"omitempty,oneof=online offline error"`
	Region  string     `form:"region" binding:"omitempty,max=50"`
	Keyword string     `form:"keyword" binding:"omitempty,max=100"`
}

// NodeHeartbeatRequest 节点心跳请求结构
type NodeHeartbeatRequest struct {
	Timestamp time.Time          `json:"timestamp" binding:"required"`
	Status    NodeStatus         `json:"status" binding:"required,oneof=online offline error"`
	Resources *NodeResourceUsage `json:"resources"`
	Services  []ServiceStatus    `json:"services"`
}

// ServiceStatus 服务状态信息
type ServiceStatus struct {
	Name   string `json:"name" binding:"required"`
	Status string `json:"status" binding:"required"`
	Port   int    `json:"port" binding:"required"`
}

// NodeControlRequest 节点控制请求结构
type NodeControlRequest struct {
	Action string                 `json:"action" binding:"required"`
	Params map[string]interface{} `json:"params"`
}

// NodeStatusUpdateRequest 节点状态更新请求
type NodeStatusUpdateRequest struct {
	Status string `json:"status" binding:"required,oneof=online offline maintenance error"`
}
