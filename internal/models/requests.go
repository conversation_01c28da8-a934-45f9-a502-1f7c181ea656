package models

import "time"

// NodeRegisterRequest 节点注册请求
type NodeRegisterRequest struct {
	ID          string `json:"id" binding:"required"`
	Name        string `json:"name" binding:"required"`
	IP          string `json:"ip" binding:"required"`
	Region      string `json:"region"`
	Description string `json:"description"`
}

// NodeCommandRequest 节点命令请求
type NodeCommandRequest struct {
	Command string                 `json:"command" binding:"required"`
	Params  map[string]interface{} `json:"params"`
}

// DeployCommandRequest 部署命令请求
type DeployCommandRequest struct {
	TemplateID string                 `json:"template_id" binding:"required"`
	Config     map[string]interface{} `json:"config"`
	Action     string                 `json:"action" binding:"required"` // deploy, stop, restart
}

// NotificationRequest 通知请求
type NotificationRequest struct {
	Title   string `json:"title" binding:"required"`
	Message string `json:"message" binding:"required"`
	Type    string `json:"type"` // info, warning, error, success
}

// AdminMessageRequest 管理员消息请求
type AdminMessageRequest struct {
	Message string `json:"message" binding:"required"`
	Type    string `json:"type"` // info, warning, error
}

// ReportRequest 报告请求
type ReportRequest struct {
	Type      string                 `json:"type" binding:"required"` // attack, node, template, system
	StartDate time.Time              `json:"start_date"`
	EndDate   time.Time              `json:"end_date"`
	NodeIDs   []string               `json:"node_ids"`
	Format    string                 `json:"format"` // json, csv, pdf
	Filters   map[string]interface{} `json:"filters"`
}

// ReportResponse 报告响应
type ReportResponse struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Status      string                 `json:"status"`
	Progress    int                    `json:"progress"`
	Data        map[string]interface{} `json:"data"`
	DownloadURL string                 `json:"download_url"`
	CreatedAt   time.Time              `json:"created_at"`
	CompletedAt *time.Time             `json:"completed_at"`
	Error       string                 `json:"error"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	User      UserInfo  `json:"user"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// UpdateProfileRequest 更新个人资料请求
type UpdateProfileRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	FullName string `json:"full_name"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	FullName string `json:"full_name"`
	Role     string `json:"role" binding:"required"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username"`
	Email    string `json:"email" binding:"email"`
	FullName string `json:"full_name"`
	Role     string `json:"role"`
	Active   *bool  `json:"active"`
}

// CreateTemplateRequest 创建模板请求
type CreateTemplateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	Type        string                 `json:"type" binding:"required"`
	ImageName   string                 `json:"image_name" binding:"required"`
	Config      map[string]interface{} `json:"config"`
	Ports       map[string]string      `json:"ports"`
	Environment map[string]string      `json:"environment"`
	Volumes     map[string]string      `json:"volumes"`
}

// UpdateTemplateRequest 更新模板请求
type UpdateTemplateRequest struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	ImageName   string                 `json:"image_name"`
	Config      map[string]interface{} `json:"config"`
	Ports       map[string]string      `json:"ports"`
	Environment map[string]string      `json:"environment"`
	Volumes     map[string]string      `json:"volumes"`
	Active      *bool                  `json:"active"`
}

// UpdateNodeRequest 更新节点请求
type UpdateNodeRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Region      string `json:"region"`
	Active      *bool  `json:"active"`
}

// PaginationRequest 分页请求
type PaginationRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Search   string `json:"search" form:"search"`
	Sort     string `json:"sort" form:"sort"`
	Order    string `json:"order" form:"order"`
}

// FilterRequest 过滤请求
type FilterRequest struct {
	StartDate time.Time              `json:"start_date" form:"start_date"`
	EndDate   time.Time              `json:"end_date" form:"end_date"`
	Status    string                 `json:"status" form:"status"`
	Type      string                 `json:"type" form:"type"`
	NodeID    string                 `json:"node_id" form:"node_id"`
	Filters   map[string]interface{} `json:"filters"`
}

// BulkActionRequest 批量操作请求
type BulkActionRequest struct {
	Action string   `json:"action" binding:"required"` // delete, activate, deactivate
	IDs    []string `json:"ids" binding:"required"`
}

// ExportRequest 导出请求
type ExportRequest struct {
	Type    string                 `json:"type" binding:"required"` // csv, json, excel
	Filters map[string]interface{} `json:"filters"`
	Fields  []string               `json:"fields"`
}

// ImportRequest 导入请求
type ImportRequest struct {
	Type string `json:"type" binding:"required"` // csv, json
	Data string `json:"data" binding:"required"`
}

// ConfigUpdateRequest 配置更新请求
type ConfigUpdateRequest struct {
	Key   string      `json:"key" binding:"required"`
	Value interface{} `json:"value" binding:"required"`
}

// BackupRequest 备份请求
type BackupRequest struct {
	Type        string   `json:"type" binding:"required"` // full, incremental
	Tables      []string `json:"tables"`
	Compression bool     `json:"compression"`
}

// RestoreRequest 恢复请求
type RestoreRequest struct {
	BackupFile string   `json:"backup_file" binding:"required"`
	Tables     []string `json:"tables"`
	Force      bool     `json:"force"`
}
