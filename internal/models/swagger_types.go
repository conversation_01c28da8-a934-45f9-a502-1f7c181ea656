package models

import (
	"time"
)

// SwaggerDeletedAt Swagger文档用的DeletedAt类型定义
// 用于替代gorm.DeletedAt，避免Swagger解析错误
type SwaggerDeletedAt struct {
	Time  time.Time `json:"time"`
	Valid bool      `json:"valid"`
}

// SwaggerServiceDeployment Swagger文档用的ServiceDeployment
// 用于API文档生成，避免gorm类型解析问题
type SwaggerServiceDeployment struct {
	ID          uint             `json:"id"`
	Name        string           `json:"name"`
	NodeID      string           `json:"node_id"`
	TemplateID  uint             `json:"template_id"`
	ImageName   string           `json:"image_name"`
	Status      string           `json:"status"`
	ContainerID string           `json:"container_id"`
	Config      string           `json:"config"`
	Ports       string           `json:"ports"`
	Environment string           `json:"environment"`
	Volumes     string           `json:"volumes"`
	StartedAt   time.Time        `json:"started_at"`
	StoppedAt   time.Time        `json:"stopped_at"`
	Error       string           `json:"error"`
	Logs        string           `json:"logs"`
	CreatedBy   string           `json:"created_by"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
	DeletedAt   SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerDeploymentLog Swagger文档用的DeploymentLog
type SwaggerDeploymentLog struct {
	ID           uint             `json:"id"`
	DeploymentID uint             `json:"deployment_id"`
	Level        string           `json:"level"`
	Message      string           `json:"message"`
	Source       string           `json:"source"`
	Timestamp    time.Time        `json:"timestamp"`
	CreatedAt    time.Time        `json:"created_at"`
	DeletedAt    SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerDeploymentMetrics Swagger文档用的DeploymentMetrics
type SwaggerDeploymentMetrics struct {
	ID           uint             `json:"id"`
	DeploymentID uint             `json:"deployment_id"`
	CPUUsage     float64          `json:"cpu_usage"`
	MemoryUsage  int64            `json:"memory_usage"`
	MemoryLimit  int64            `json:"memory_limit"`
	NetworkRx    int64            `json:"network_rx"`
	NetworkTx    int64            `json:"network_tx"`
	DiskRead     int64            `json:"disk_read"`
	DiskWrite    int64            `json:"disk_write"`
	Timestamp    time.Time        `json:"timestamp"`
	CreatedAt    time.Time        `json:"created_at"`
	DeletedAt    SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerDeploymentCommand Swagger文档用的DeploymentCommand
type SwaggerDeploymentCommand struct {
	ID           uint             `json:"id"`
	DeploymentID uint             `json:"deployment_id"`
	Command      string           `json:"command"`
	Args         string           `json:"args"`
	Status       string           `json:"status"`
	Result       string           `json:"result"`
	Error        string           `json:"error"`
	ExecutedBy   string           `json:"executed_by"`
	ExecutedAt   *time.Time       `json:"executed_at"`
	CompletedAt  *time.Time       `json:"completed_at"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
	DeletedAt    SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerHealthCheck Swagger文档用的HealthCheck
type SwaggerHealthCheck struct {
	ID           uint             `json:"id"`
	DeploymentID uint             `json:"deployment_id"`
	Type         string           `json:"type"`
	Endpoint     string           `json:"endpoint"`
	Status       string           `json:"status"`
	ResponseTime int64            `json:"response_time"`
	StatusCode   int              `json:"status_code"`
	Message      string           `json:"message"`
	CheckedAt    time.Time        `json:"checked_at"`
	CreatedAt    time.Time        `json:"created_at"`
	DeletedAt    SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerNode Swagger文档用的Node
type SwaggerNode struct {
	ID              string           `json:"id"`
	Name            string           `json:"name"`
	IP              string           `json:"ip"`
	Port            int              `json:"port"`
	Region          string           `json:"region"`
	Status          string           `json:"status"`
	Version         string           `json:"version"`
	OS              string           `json:"os"`
	Arch            string           `json:"arch"`
	CPUCores        int              `json:"cpu_cores"`
	Memory          int64            `json:"memory"`
	Disk            int64            `json:"disk"`
	Description     string           `json:"description"`
	Tags            string           `json:"tags"`
	LastHeartbeatAt time.Time        `json:"last_heartbeat_at"`
	LastConnectedAt time.Time        `json:"last_connected_at"`
	CreatedAt       time.Time        `json:"created_at"`
	UpdatedAt       time.Time        `json:"updated_at"`
	DeletedAt       SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerTemplate Swagger文档用的Template
type SwaggerTemplate struct {
	ID                   uint             `json:"id"`
	Name                 string           `json:"name"`
	Description          string           `json:"description"`
	Type                 string           `json:"type"`
	ImageName            string           `json:"image_name"`
	ImageTag             string           `json:"image_tag"`
	Version              string           `json:"version"`
	Status               string           `json:"status"`
	ConfigSchema         string           `json:"config_schema"`
	DefaultConfig        string           `json:"default_config"`
	ResourceRequirements string           `json:"resource_requirements"`
	Tags                 string           `json:"tags"`
	Author               string           `json:"author"`
	Documentation        string           `json:"documentation"`
	Active               bool             `json:"active"`
	CreatedBy            string           `json:"created_by"`
	CreatedAt            time.Time        `json:"created_at"`
	UpdatedAt            time.Time        `json:"updated_at"`
	DeletedAt            SwaggerDeletedAt `json:"deleted_at"`
}

// SwaggerDeploymentBackup Swagger文档用的DeploymentBackup
type SwaggerDeploymentBackup struct {
	ID           uint             `json:"id"`
	DeploymentID uint             `json:"deployment_id"`
	BackupPath   string           `json:"backup_path"`
	BackupSize   int64            `json:"backup_size"`
	BackupType   string           `json:"backup_type"`
	Status       string           `json:"status"`
	Description  string           `json:"description"`
	CreatedBy    string           `json:"created_by"`
	CreatedAt    time.Time        `json:"created_at"`
	CompletedAt  *time.Time       `json:"completed_at"`
	DeletedAt    SwaggerDeletedAt `json:"deleted_at"`
}
