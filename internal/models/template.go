package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// TemplateType 模板类型枚举
type TemplateType string

const (
	TemplateTypeSSH    TemplateType = "ssh"    // SSH蜜罐
	TemplateTypeWeb    TemplateType = "web"    // Web蜜罐
	TemplateTypeFTP    TemplateType = "ftp"    // FTP蜜罐
	TemplateTypeTelnet TemplateType = "telnet" // Telnet蜜罐
	TemplateTypeCustom TemplateType = "custom" // 自定义蜜罐
)

// TemplateStatus 模板状态枚举
type TemplateStatus string

const (
	TemplateStatusActive     TemplateStatus = "active"     // 活跃
	TemplateStatusInactive   TemplateStatus = "inactive"   // 非活跃
	TemplateStatusProcessing TemplateStatus = "processing" // 处理中
)

// ConfigSchema 配置参数定义
type ConfigSchema map[string]ConfigParam

// ConfigParam 配置参数
type ConfigParam struct {
	Type        string      `json:"type"`                 // 参数类型：string, integer, boolean
	Description string      `json:"description"`          // 参数描述
	Default     interface{} `json:"default"`              // 默认值
	Required    bool        `json:"required,omitempty"`   // 是否必需
	Min         *int        `json:"min,omitempty"`        // 最小值（数字类型）
	Max         *int        `json:"max,omitempty"`        // 最大值（数字类型）
	MaxLength   *int        `json:"max_length,omitempty"` // 最大长度（字符串类型）
	Options     []string    `json:"options,omitempty"`    // 可选值列表
}

// DefaultConfig 默认配置
type DefaultConfig map[string]interface{}

// ResourceRequirements 资源需求
type ResourceRequirements struct {
	Memory string `json:"memory"` // 内存需求，如："128Mi"
	CPU    string `json:"cpu"`    // CPU需求，如："100m"
}

// Template 模板模型
type Template struct {
	ID                   string                `gorm:"type:varchar(50);primaryKey;<-:create" json:"id"`
	Name                 string                `gorm:"type:varchar(100);not null" json:"name"`
	Type                 TemplateType          `gorm:"type:varchar(20);not null" json:"type"`
	Description          string                `gorm:"type:text" json:"description"`
	ImageName            string                `gorm:"type:varchar(255);not null" json:"image_name"`
	ImageTag             string                `gorm:"type:varchar(50);default:'latest'" json:"image_tag"`
	FileName             string                `gorm:"type:varchar(255)" json:"file_name"`
	FileSize             int64                 `gorm:"type:bigint" json:"file_size"`
	FileHash             string                `gorm:"type:varchar(64)" json:"file_hash"`
	Version              string                `gorm:"type:varchar(20);default:'1.0.0'" json:"version"`
	Status               TemplateStatus        `gorm:"type:varchar(20);default:'active'" json:"status"`
	Active               bool                  `gorm:"type:boolean;default:true" json:"active"`
	ConfigSchema         ConfigSchema          `gorm:"type:json" json:"config_schema"`
	DefaultConfig        DefaultConfig         `gorm:"type:json" json:"default_config"`
	ResourceRequirements *ResourceRequirements `gorm:"type:json" json:"resource_requirements"`
	UploadedAt           time.Time             `json:"uploaded_at"`
	CreatedAt            time.Time             `json:"created_at"`
	UpdatedAt            time.Time             `json:"updated_at"`

	// 关联关系
	Deployments []ServiceDeployment `gorm:"foreignKey:TemplateID;constraint:OnDelete:CASCADE" json:"deployments,omitempty"`
}

// TableName 指定表名
func (Template) TableName() string {
	return "templates"
}

// 实现 GORM 的 Valuer 和 Scanner 接口，用于JSON字段的序列化

// Value 实现 driver.Valuer 接口
func (cs ConfigSchema) Value() (driver.Value, error) {
	return json.Marshal(cs)
}

// Scan 实现 sql.Scanner 接口
func (cs *ConfigSchema) Scan(value interface{}) error {
	if value == nil {
		*cs = make(ConfigSchema)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, cs)
}

// Value 实现 driver.Valuer 接口
func (dc DefaultConfig) Value() (driver.Value, error) {
	return json.Marshal(dc)
}

// Scan 实现 sql.Scanner 接口
func (dc *DefaultConfig) Scan(value interface{}) error {
	if value == nil {
		*dc = make(DefaultConfig)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, dc)
}

// Value 实现 driver.Valuer 接口
func (rr ResourceRequirements) Value() (driver.Value, error) {
	return json.Marshal(rr)
}

// Scan 实现 sql.Scanner 接口
func (rr *ResourceRequirements) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, rr)
}

// GetTypeDisplayName 获取类型显示名称
func (t *Template) GetTypeDisplayName() string {
	switch t.Type {
	case TemplateTypeSSH:
		return "SSH蜜罐"
	case TemplateTypeWeb:
		return "Web蜜罐"
	case TemplateTypeFTP:
		return "FTP蜜罐"
	case TemplateTypeTelnet:
		return "Telnet蜜罐"
	case TemplateTypeCustom:
		return "自定义蜜罐"
	default:
		return "未知类型"
	}
}

// GetStatusDisplayName 获取状态显示名称
func (t *Template) GetStatusDisplayName() string {
	switch t.Status {
	case TemplateStatusActive:
		return "活跃"
	case TemplateStatusInactive:
		return "非活跃"
	case TemplateStatusProcessing:
		return "处理中"
	default:
		return "未知"
	}
}

// IsActive 检查模板是否活跃
func (t *Template) IsActive() bool {
	return t.Status == TemplateStatusActive
}

// TemplateCreateRequest 模板创建请求结构
type TemplateCreateRequest struct {
	Name                 string                `json:"name" binding:"required,min=1,max=100"`
	Type                 TemplateType          `json:"type" binding:"required,oneof=ssh web ftp telnet custom"`
	Description          string                `json:"description" binding:"omitempty,max=1000"`
	ImageName            string                `json:"image_name" binding:"required,max=255"`
	ImageTag             string                `json:"image_tag" binding:"omitempty,max=50"`
	Version              string                `json:"version" binding:"omitempty,max=20"`
	ConfigSchema         ConfigSchema          `json:"config_schema"`
	DefaultConfig        DefaultConfig         `json:"default_config"`
	ResourceRequirements *ResourceRequirements `json:"resource_requirements"`
}

// TemplateUpdateRequest 模板更新请求结构
type TemplateUpdateRequest struct {
	Name                 string                `json:"name" binding:"omitempty,min=1,max=100"`
	Description          string                `json:"description" binding:"omitempty,max=1000"`
	Status               TemplateStatus        `json:"status" binding:"omitempty,oneof=active inactive"`
	ConfigSchema         ConfigSchema          `json:"config_schema"`
	DefaultConfig        DefaultConfig         `json:"default_config"`
	ResourceRequirements *ResourceRequirements `json:"resource_requirements"`
}

// TemplateListRequest 模板列表请求结构
type TemplateListRequest struct {
	Page    int            `form:"page,default=1" binding:"min=1"`
	Size    int            `form:"size,default=20" binding:"min=1,max=100"`
	Type    TemplateType   `form:"type" binding:"omitempty,oneof=ssh web ftp telnet custom"`
	Status  TemplateStatus `form:"status" binding:"omitempty,oneof=active inactive processing"`
	Keyword string         `form:"keyword" binding:"omitempty,max=100"`
}

// TemplateDeployRequest 模板部署请求结构
type TemplateDeployRequest struct {
	NodeIDs []string               `json:"node_ids" binding:"required,min=1"`
	Config  map[string]interface{} `json:"config" binding:"required"`
}

// TemplateResponse 模板响应结构
type TemplateResponse struct {
	ID                   string                `json:"id"`
	Name                 string                `json:"name"`
	Type                 TemplateType          `json:"type"`
	TypeName             string                `json:"type_name"`
	Description          string                `json:"description"`
	ImageName            string                `json:"image_name"`
	ImageTag             string                `json:"image_tag"`
	Version              string                `json:"version"`
	Status               TemplateStatus        `json:"status"`
	StatusName           string                `json:"status_name"`
	ConfigSchema         ConfigSchema          `json:"config_schema"`
	DefaultConfig        DefaultConfig         `json:"default_config"`
	ResourceRequirements *ResourceRequirements `json:"resource_requirements"`
	UploadedAt           time.Time             `json:"uploaded_at"`
	CreatedAt            time.Time             `json:"created_at"`
	UpdatedAt            time.Time             `json:"updated_at"`
}

// ToTemplateResponse 转换为模板响应结构
func (t *Template) ToTemplateResponse() *TemplateResponse {
	return &TemplateResponse{
		ID:                   t.ID,
		Name:                 t.Name,
		Type:                 t.Type,
		TypeName:             t.GetTypeDisplayName(),
		Description:          t.Description,
		ImageName:            t.ImageName,
		ImageTag:             t.ImageTag,
		Version:              t.Version,
		Status:               t.Status,
		StatusName:           t.GetStatusDisplayName(),
		ConfigSchema:         t.ConfigSchema,
		DefaultConfig:        t.DefaultConfig,
		ResourceRequirements: t.ResourceRequirements,
		UploadedAt:           t.UploadedAt,
		CreatedAt:            t.CreatedAt,
		UpdatedAt:            t.UpdatedAt,
	}
}
