package models

import (
	"time"

	"gorm.io/gorm"
)

// UserRole 用户角色枚举
type UserRole string

const (
	RoleAdministrator UserRole = "administrator" // 管理员 - 全部功能权限
	RoleOperator      UserRole = "operator"      // 操作员 - 节点管理权限
	RoleObserver      UserRole = "observer"      // 观察者 - 仅查看权限
)

// UserStatus 用户状态枚举
type UserStatus string

const (
	StatusActive   UserStatus = "active"   // 活跃
	StatusInactive UserStatus = "inactive" // 非活跃
)

// User 用户模型
type User struct {
	ID          uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	Username    string     `gorm:"type:varchar(50);uniqueIndex;not null" json:"username"`
	Password    string     `gorm:"type:varchar(255);not null" json:"-"` // 不在JSON中返回密码
	Email       string     `gorm:"type:varchar(100)" json:"email"`
	Role        UserRole   `gorm:"type:varchar(20);default:'observer'" json:"role"`
	Status      UserStatus `gorm:"type:varchar(20);default:'active'" json:"status"`
	LastLoginAt *time.Time `json:"last_login_at"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// BeforeCreate GORM钩子 - 创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 可以在这里添加创建前的逻辑，如密码加密等
	return nil
}

// HasPermission 检查用户是否有指定权限
func (u *User) HasPermission(permission string) bool {
	switch u.Role {
	case RoleAdministrator:
		return true // 管理员有所有权限
	case RoleOperator:
		// 操作员权限列表
		operatorPermissions := []string{
			"node_manage", "template_manage", "service_deploy", "data_view",
		}
		for _, p := range operatorPermissions {
			if p == permission {
				return true
			}
		}
	case RoleObserver:
		// 观察者权限列表
		observerPermissions := []string{
			"data_view",
		}
		for _, p := range observerPermissions {
			if p == permission {
				return true
			}
		}
	}
	return false
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == StatusActive
}

// GetRoleDisplayName 获取角色显示名称
func (u *User) GetRoleDisplayName() string {
	switch u.Role {
	case RoleAdministrator:
		return "管理员"
	case RoleOperator:
		return "操作员"
	case RoleObserver:
		return "观察者"
	default:
		return "未知"
	}
}

// UserCreateRequest 用户创建请求结构
type UserCreateRequest struct {
	Username string   `json:"username" binding:"required,min=3,max=50"`
	Password string   `json:"password" binding:"required,min=6"`
	Email    string   `json:"email" binding:"omitempty,email"`
	Role     UserRole `json:"role" binding:"required,oneof=administrator operator observer"`
}

// UserUpdateRequest 用户更新请求结构
type UserUpdateRequest struct {
	Email  string     `json:"email" binding:"omitempty,email"`
	Role   UserRole   `json:"role" binding:"omitempty,oneof=administrator operator observer"`
	Status UserStatus `json:"status" binding:"omitempty,oneof=active inactive"`
}

// UserLoginRequest 用户登录请求结构
type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// UserRegisterRequest 用户注册请求结构
type UserRegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Password string `json:"password" binding:"required,min=6"`
	Email    string `json:"email" binding:"required,email"`
	Role     string `json:"role" binding:"omitempty,oneof=administrator operator observer"`
}

// UserLoginResponse 用户登录响应结构
type UserLoginResponse struct {
	Token     string    `json:"token"`
	ExpiresIn int       `json:"expires_in"`
	UserInfo  *UserInfo `json:"user_info"`
}

// UserInfo 用户信息结构（用于响应）
type UserInfo struct {
	ID       uint     `json:"id"`
	Username string   `json:"username"`
	Email    string   `json:"email"`
	Role     UserRole `json:"role"`
	RoleName string   `json:"role_name"`
}

// ToUserInfo 转换为用户信息结构
func (u *User) ToUserInfo() *UserInfo {
	return &UserInfo{
		ID:       u.ID,
		Username: u.Username,
		Email:    u.Email,
		Role:     u.Role,
		RoleName: u.GetRoleDisplayName(),
	}
}

// PasswordChangeRequest 密码修改请求结构
type PasswordChangeRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=6"`
}
