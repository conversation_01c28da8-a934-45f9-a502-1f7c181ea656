package services

import (
	"context"
	"fmt"
	"time"
)

// AttackAnalysisService 攻击数据分析服务
type AttackAnalysisService struct {
	decoyWatchService *DecoyWatchService
}

// NewAttackAnalysisService 创建攻击数据分析服务实例
func NewAttackAnalysisService(decoyWatchService *DecoyWatchService) *AttackAnalysisService {
	return &AttackAnalysisService{
		decoyWatchService: decoyWatchService,
	}
}

// AttackTrendAnalysis 攻击趋势分析结果
type AttackTrendAnalysis struct {
	TimeRange    string             `json:"time_range"`
	TotalAttacks int64              `json:"total_attacks"`
	TrendData    []AttackTrendPoint `json:"trend_data"`
	GrowthRate   float64            `json:"growth_rate"`
	PeakHours    []int              `json:"peak_hours"`
	Summary      AttackTrendSummary `json:"summary"`
}

// AttackTrendPoint 攻击趋势数据点
type AttackTrendPoint struct {
	Timestamp   time.Time `json:"timestamp"`
	AttackCount int64     `json:"attack_count"`
	UniqueIPs   int64     `json:"unique_ips"`
	AttackTypes int64     `json:"attack_types"`
}

// AttackTrendSummary 攻击趋势摘要
type AttackTrendSummary struct {
	AvgAttacksPerHour float64 `json:"avg_attacks_per_hour"`
	MaxAttacksPerHour int64   `json:"max_attacks_per_hour"`
	MinAttacksPerHour int64   `json:"min_attacks_per_hour"`
	TrendDirection    string  `json:"trend_direction"` // "increasing", "decreasing", "stable"
}

// IPGeographicAnalysis IP地理分布分析结果
type IPGeographicAnalysis struct {
	TotalUniqueIPs    int64                    `json:"total_unique_ips"`
	CountryStats      []CountryAttackStats     `json:"country_stats"`
	RegionStats       []RegionAttackStats      `json:"region_stats"`
	TopAttackerIPs    []TopAttackerIP          `json:"top_attacker_ips"`
	GeographicHeatmap []GeographicHeatmapPoint `json:"geographic_heatmap"`
}

// CountryAttackStats 国家攻击统计
type CountryAttackStats struct {
	Country     string  `json:"country"`
	CountryCode string  `json:"country_code"`
	AttackCount int64   `json:"attack_count"`
	UniqueIPs   int64   `json:"unique_ips"`
	Percentage  float64 `json:"percentage"`
}

// RegionAttackStats 地区攻击统计
type RegionAttackStats struct {
	Region      string  `json:"region"`
	AttackCount int64   `json:"attack_count"`
	UniqueIPs   int64   `json:"unique_ips"`
	Percentage  float64 `json:"percentage"`
}

// TopAttackerIP 顶级攻击者IP
type TopAttackerIP struct {
	IP          string    `json:"ip"`
	AttackCount int64     `json:"attack_count"`
	Country     string    `json:"country"`
	Region      string    `json:"region"`
	FirstSeen   time.Time `json:"first_seen"`
	LastSeen    time.Time `json:"last_seen"`
	AttackTypes []string  `json:"attack_types"`
	ThreatLevel string    `json:"threat_level"` // "low", "medium", "high", "critical"
}

// GeographicHeatmapPoint 地理热力图数据点
type GeographicHeatmapPoint struct {
	Latitude    float64 `json:"latitude"`
	Longitude   float64 `json:"longitude"`
	AttackCount int64   `json:"attack_count"`
	Country     string  `json:"country"`
	City        string  `json:"city"`
}

// AttackTypeAnalysis 攻击类型分析结果
type AttackTypeAnalysis struct {
	TotalAttackTypes int64                      `json:"total_attack_types"`
	TypeDistribution []AttackTypeDistribution   `json:"type_distribution"`
	TypeTrends       []AttackTypeTrend          `json:"type_trends"`
	TypeCorrelation  []AttackTypeCorrelation    `json:"type_correlation"`
	ThreatAssessment AttackTypeThreatAssessment `json:"threat_assessment"`
}

// AttackTypeDistribution 攻击类型分布
type AttackTypeDistribution struct {
	AttackType  string  `json:"attack_type"`
	Count       int64   `json:"count"`
	Percentage  float64 `json:"percentage"`
	Severity    string  `json:"severity"`
	Description string  `json:"description"`
}

// AttackTypeTrend 攻击类型趋势
type AttackTypeTrend struct {
	AttackType  string             `json:"attack_type"`
	TrendData   []AttackTrendPoint `json:"trend_data"`
	GrowthRate  float64            `json:"growth_rate"`
	TrendStatus string             `json:"trend_status"` // "rising", "falling", "stable"
}

// AttackTypeCorrelation 攻击类型关联分析
type AttackTypeCorrelation struct {
	PrimaryType   string  `json:"primary_type"`
	SecondaryType string  `json:"secondary_type"`
	Correlation   float64 `json:"correlation"`
	CoOccurrence  int64   `json:"co_occurrence"`
}

// AttackTypeThreatAssessment 攻击类型威胁评估
type AttackTypeThreatAssessment struct {
	HighThreatTypes    []string `json:"high_threat_types"`
	EmergingThreats    []string `json:"emerging_threats"`
	DecreasingThreats  []string `json:"decreasing_threats"`
	OverallThreatLevel string   `json:"overall_threat_level"`
}

// AnalyzeAttackTrends 分析攻击趋势
func (s *AttackAnalysisService) AnalyzeAttackTrends(ctx context.Context, timeRange string, nodeID string) (*AttackTrendAnalysis, error) {
	if !s.decoyWatchService.IsEnabled() {
		return nil, fmt.Errorf("DecoyWatch服务未启用")
	}

	// 解析时间范围
	startTime, endTime, err := s.parseTimeRange(timeRange)
	if err != nil {
		return nil, fmt.Errorf("无效的时间范围: %w", err)
	}

	// 获取基础统计数据
	stats, err := s.decoyWatchService.GetAttackStatistics(ctx, nodeID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取攻击统计失败: %w", err)
	}

	// 构建趋势分析结果
	analysis := &AttackTrendAnalysis{
		TimeRange:    timeRange,
		TotalAttacks: stats.TotalAttacks,
		TrendData:    s.buildTrendData(stats.HourlyStats),
		PeakHours:    s.findPeakHours(stats.HourlyStats),
		Summary:      s.calculateTrendSummary(stats.HourlyStats),
	}

	// 计算增长率
	analysis.GrowthRate = s.calculateGrowthRate(stats.HourlyStats)

	return analysis, nil
}

// AnalyzeIPGeographic 分析IP地理分布
func (s *AttackAnalysisService) AnalyzeIPGeographic(ctx context.Context, timeRange string, nodeID string) (*IPGeographicAnalysis, error) {
	if !s.decoyWatchService.IsEnabled() {
		return nil, fmt.Errorf("DecoyWatch服务未启用")
	}

	// 获取IP统计数据
	ipStats, err := s.decoyWatchService.GetIPStatistics(ctx, 100) // 获取更多IP数据用于分析
	if err != nil {
		return nil, fmt.Errorf("获取IP统计失败: %w", err)
	}

	// 构建地理分析结果
	analysis := &IPGeographicAnalysis{
		TotalUniqueIPs: int64(len(ipStats)),
		CountryStats:   s.buildCountryStats(ipStats),
		TopAttackerIPs: s.buildTopAttackerIPs(ipStats),
	}

	return analysis, nil
}

// AnalyzeAttackTypes 分析攻击类型
func (s *AttackAnalysisService) AnalyzeAttackTypes(ctx context.Context, timeRange string, nodeID string) (*AttackTypeAnalysis, error) {
	if !s.decoyWatchService.IsEnabled() {
		return nil, fmt.Errorf("DecoyWatch服务未启用")
	}

	// 解析时间范围
	startTime, endTime, err := s.parseTimeRange(timeRange)
	if err != nil {
		return nil, fmt.Errorf("无效的时间范围: %w", err)
	}

	// 获取攻击统计数据
	stats, err := s.decoyWatchService.GetAttackStatistics(ctx, nodeID, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("获取攻击统计失败: %w", err)
	}

	// 构建攻击类型分析结果
	analysis := &AttackTypeAnalysis{
		TotalAttackTypes: int64(len(stats.TopAttackTypes)),
		TypeDistribution: s.buildTypeDistribution(stats.TopAttackTypes),
		ThreatAssessment: s.assessThreatLevel(stats.TopAttackTypes),
	}

	return analysis, nil
}

// parseTimeRange 解析时间范围
func (s *AttackAnalysisService) parseTimeRange(timeRange string) (*time.Time, *time.Time, error) {
	now := time.Now()
	var startTime, endTime time.Time

	switch timeRange {
	case "1h":
		startTime = now.Add(-1 * time.Hour)
		endTime = now
	case "24h":
		startTime = now.Add(-24 * time.Hour)
		endTime = now
	case "7d":
		startTime = now.Add(-7 * 24 * time.Hour)
		endTime = now
	case "30d":
		startTime = now.Add(-30 * 24 * time.Hour)
		endTime = now
	default:
		return nil, nil, fmt.Errorf("不支持的时间范围: %s", timeRange)
	}

	return &startTime, &endTime, nil
}

// buildTrendData 构建趋势数据
func (s *AttackAnalysisService) buildTrendData(hourlyStats []HourlyAttackCount) []AttackTrendPoint {
	trendData := make([]AttackTrendPoint, len(hourlyStats))

	for i, stat := range hourlyStats {
		trendData[i] = AttackTrendPoint{
			Timestamp:   time.Now().Add(time.Duration(stat.Hour-24) * time.Hour),
			AttackCount: stat.Count,
			UniqueIPs:   0, // 需要从详细数据中获取
			AttackTypes: 0, // 需要从详细数据中获取
		}
	}

	return trendData
}

// findPeakHours 找出攻击高峰时段
func (s *AttackAnalysisService) findPeakHours(hourlyStats []HourlyAttackCount) []int {
	if len(hourlyStats) == 0 {
		return []int{}
	}

	// 计算平均值
	var total int64
	for _, stat := range hourlyStats {
		total += stat.Count
	}
	avg := float64(total) / float64(len(hourlyStats))

	// 找出超过平均值1.5倍的时段
	var peakHours []int
	for _, stat := range hourlyStats {
		if float64(stat.Count) > avg*1.5 {
			peakHours = append(peakHours, stat.Hour)
		}
	}

	return peakHours
}

// calculateTrendSummary 计算趋势摘要
func (s *AttackAnalysisService) calculateTrendSummary(hourlyStats []HourlyAttackCount) AttackTrendSummary {
	if len(hourlyStats) == 0 {
		return AttackTrendSummary{}
	}

	var total, max, min int64
	min = hourlyStats[0].Count

	for _, stat := range hourlyStats {
		total += stat.Count
		if stat.Count > max {
			max = stat.Count
		}
		if stat.Count < min {
			min = stat.Count
		}
	}

	avg := float64(total) / float64(len(hourlyStats))

	// 简单的趋势判断
	var trendDirection string
	if len(hourlyStats) >= 2 {
		recent := hourlyStats[len(hourlyStats)-1].Count
		previous := hourlyStats[len(hourlyStats)-2].Count

		if recent > previous {
			trendDirection = "increasing"
		} else if recent < previous {
			trendDirection = "decreasing"
		} else {
			trendDirection = "stable"
		}
	} else {
		trendDirection = "stable"
	}

	return AttackTrendSummary{
		AvgAttacksPerHour: avg,
		MaxAttacksPerHour: max,
		MinAttacksPerHour: min,
		TrendDirection:    trendDirection,
	}
}

// calculateGrowthRate 计算增长率
func (s *AttackAnalysisService) calculateGrowthRate(hourlyStats []HourlyAttackCount) float64 {
	if len(hourlyStats) < 2 {
		return 0.0
	}

	// 计算最近一半时间与前一半时间的增长率
	mid := len(hourlyStats) / 2

	var firstHalf, secondHalf int64
	for i := 0; i < mid; i++ {
		firstHalf += hourlyStats[i].Count
	}
	for i := mid; i < len(hourlyStats); i++ {
		secondHalf += hourlyStats[i].Count
	}

	if firstHalf == 0 {
		return 0.0
	}

	return float64(secondHalf-firstHalf) / float64(firstHalf) * 100
}

// buildCountryStats 构建国家统计数据
func (s *AttackAnalysisService) buildCountryStats(ipStats []SourceIPCount) []CountryAttackStats {
	countryMap := make(map[string]*CountryAttackStats)
	totalAttacks := int64(0)

	for _, ip := range ipStats {
		// 根据IP地址推断国家（简化版本）
		country := s.getCountryByIP(ip.SourceIP)
		if country == "" {
			country = "Unknown"
		}

		if stat, exists := countryMap[country]; exists {
			stat.AttackCount += ip.Count
			stat.UniqueIPs++
		} else {
			countryMap[country] = &CountryAttackStats{
				Country:     country,
				CountryCode: s.getCountryCode(country),
				AttackCount: ip.Count,
				UniqueIPs:   1,
			}
		}
		totalAttacks += ip.Count
	}

	// 转换为切片并计算百分比
	var countryStats []CountryAttackStats
	for _, stat := range countryMap {
		stat.Percentage = float64(stat.AttackCount) / float64(totalAttacks) * 100
		countryStats = append(countryStats, *stat)
	}

	// 按攻击次数排序
	for i := 0; i < len(countryStats)-1; i++ {
		for j := i + 1; j < len(countryStats); j++ {
			if countryStats[i].AttackCount < countryStats[j].AttackCount {
				countryStats[i], countryStats[j] = countryStats[j], countryStats[i]
			}
		}
	}

	return countryStats
}

// buildTopAttackerIPs 构建顶级攻击者IP列表
func (s *AttackAnalysisService) buildTopAttackerIPs(ipStats []SourceIPCount) []TopAttackerIP {
	var topAttackers []TopAttackerIP

	for i, ip := range ipStats {
		if i >= 20 { // 只取前20个
			break
		}

		threatLevel := s.assessIPThreatLevel(ip.Count)

		topAttacker := TopAttackerIP{
			IP:          ip.SourceIP,
			AttackCount: ip.Count,
			Country:     s.getCountryByIP(ip.SourceIP),
			ThreatLevel: threatLevel,
			FirstSeen:   time.Now().Add(-24 * time.Hour), // 模拟数据
			LastSeen:    time.Now(),
			AttackTypes: []string{"SSH_BRUTE_FORCE", "WEB_SCAN"}, // 模拟数据
		}

		topAttackers = append(topAttackers, topAttacker)
	}

	return topAttackers
}

// buildTypeDistribution 构建攻击类型分布
func (s *AttackAnalysisService) buildTypeDistribution(attackTypes []AttackTypeCount) []AttackTypeDistribution {
	var totalAttacks int64
	for _, at := range attackTypes {
		totalAttacks += at.Count
	}

	var distribution []AttackTypeDistribution
	for _, at := range attackTypes {
		percentage := float64(at.Count) / float64(totalAttacks) * 100

		dist := AttackTypeDistribution{
			AttackType:  at.AttackType,
			Count:       at.Count,
			Percentage:  percentage,
			Severity:    s.getAttackTypeSeverity(at.AttackType),
			Description: s.getAttackTypeDescription(at.AttackType),
		}

		distribution = append(distribution, dist)
	}

	return distribution
}

// assessThreatLevel 评估威胁级别
func (s *AttackAnalysisService) assessThreatLevel(attackTypes []AttackTypeCount) AttackTypeThreatAssessment {
	var highThreatTypes, emergingThreats, decreasingThreats []string

	for _, at := range attackTypes {
		severity := s.getAttackTypeSeverity(at.AttackType)

		switch severity {
		case "high", "critical":
			highThreatTypes = append(highThreatTypes, at.AttackType)
		}

		// 简单的新兴威胁检测逻辑
		if at.Count > 100 { // 假设超过100次的为新兴威胁
			emergingThreats = append(emergingThreats, at.AttackType)
		}
	}

	// 评估整体威胁级别
	overallThreatLevel := "medium"
	if len(highThreatTypes) > 3 {
		overallThreatLevel = "high"
	} else if len(highThreatTypes) == 0 {
		overallThreatLevel = "low"
	}

	return AttackTypeThreatAssessment{
		HighThreatTypes:    highThreatTypes,
		EmergingThreats:    emergingThreats,
		DecreasingThreats:  decreasingThreats,
		OverallThreatLevel: overallThreatLevel,
	}
}

// getCountryByIP 根据IP地址推断国家（简化版本）
func (s *AttackAnalysisService) getCountryByIP(ip string) string {
	// 简化的IP地理位置映射，实际应用中应使用专业的IP地理位置数据库
	ipCountryMap := map[string]string{
		"192.168.": "Private Network",
		"10.":      "Private Network",
		"172.16.":  "Private Network",
		"172.17.":  "Private Network",
		"172.18.":  "Private Network",
		"172.19.":  "Private Network",
		"172.20.":  "Private Network",
		"172.21.":  "Private Network",
		"172.22.":  "Private Network",
		"172.23.":  "Private Network",
		"172.24.":  "Private Network",
		"172.25.":  "Private Network",
		"172.26.":  "Private Network",
		"172.27.":  "Private Network",
		"172.28.":  "Private Network",
		"172.29.":  "Private Network",
		"172.30.":  "Private Network",
		"172.31.":  "Private Network",
		"127.":     "Localhost",
		"8.8.":     "United States",
		"1.1.":     "United States",
		"114.114.": "China",
		"223.5.":   "China",
	}

	for prefix, country := range ipCountryMap {
		if len(ip) >= len(prefix) && ip[:len(prefix)] == prefix {
			return country
		}
	}

	// 根据IP段进行更粗略的判断
	if len(ip) > 0 {
		firstOctet := ip[:3]
		switch {
		case firstOctet >= "001" && firstOctet <= "126":
			return "United States"
		case firstOctet >= "128" && firstOctet <= "191":
			return "Europe"
		case firstOctet >= "192" && firstOctet <= "223":
			return "Asia"
		default:
			return "Unknown"
		}
	}

	return "Unknown"
}

// getCountryCode 获取国家代码
func (s *AttackAnalysisService) getCountryCode(country string) string {
	countryCodeMap := map[string]string{
		"China":           "CN",
		"United States":   "US",
		"Russia":          "RU",
		"Germany":         "DE",
		"Japan":           "JP",
		"United Kingdom":  "GB",
		"France":          "FR",
		"India":           "IN",
		"Brazil":          "BR",
		"Canada":          "CA",
		"Europe":          "EU",
		"Asia":            "AS",
		"Private Network": "PR",
		"Localhost":       "LH",
		"Unknown":         "XX",
	}

	if code, exists := countryCodeMap[country]; exists {
		return code
	}
	return "XX"
}

// assessIPThreatLevel 评估IP威胁级别
func (s *AttackAnalysisService) assessIPThreatLevel(attackCount int64) string {
	if attackCount >= 1000 {
		return "critical"
	} else if attackCount >= 500 {
		return "high"
	} else if attackCount >= 100 {
		return "medium"
	}
	return "low"
}

// getAttackTypeSeverity 获取攻击类型严重程度
func (s *AttackAnalysisService) getAttackTypeSeverity(attackType string) string {
	severityMap := map[string]string{
		"ssh_brute_force":    "high",
		"SSH_BRUTE_FORCE":    "high",
		"web_scan":           "medium",
		"WEB_SCAN":           "medium",
		"http_scan":          "medium",
		"HTTP_SCAN":          "medium",
		"port_scan":          "medium",
		"PORT_SCAN":          "medium",
		"sql_injection":      "critical",
		"SQL_INJECTION":      "critical",
		"xss":                "high",
		"XSS":                "high",
		"malware_download":   "critical",
		"MALWARE_DOWNLOAD":   "critical",
		"ddos":               "critical",
		"DDoS":               "critical",
		"telnet_brute_force": "high",
		"TELNET_BRUTE_FORCE": "high",
		"ftp_brute_force":    "medium",
		"FTP_BRUTE_FORCE":    "medium",
		"http_flood":         "high",
		"HTTP_FLOOD":         "high",
	}

	if severity, exists := severityMap[attackType]; exists {
		return severity
	}
	return "medium"
}

// getAttackTypeDescription 获取攻击类型描述
func (s *AttackAnalysisService) getAttackTypeDescription(attackType string) string {
	descriptionMap := map[string]string{
		"ssh_brute_force":    "SSH暴力破解攻击，尝试通过字典攻击获取SSH访问权限",
		"SSH_BRUTE_FORCE":    "SSH暴力破解攻击，尝试通过字典攻击获取SSH访问权限",
		"web_scan":           "Web应用扫描，探测Web应用的漏洞和敏感信息",
		"WEB_SCAN":           "Web应用扫描，探测Web应用的漏洞和敏感信息",
		"http_scan":          "HTTP扫描，探测Web服务器的漏洞和配置信息",
		"HTTP_SCAN":          "HTTP扫描，探测Web服务器的漏洞和配置信息",
		"port_scan":          "端口扫描，探测目标主机开放的端口和服务",
		"PORT_SCAN":          "端口扫描，探测目标主机开放的端口和服务",
		"sql_injection":      "SQL注入攻击，尝试通过恶意SQL语句获取数据库访问权限",
		"SQL_INJECTION":      "SQL注入攻击，尝试通过恶意SQL语句获取数据库访问权限",
		"xss":                "跨站脚本攻击，在Web页面中注入恶意脚本",
		"XSS":                "跨站脚本攻击，在Web页面中注入恶意脚本",
		"malware_download":   "恶意软件下载，尝试在目标系统中植入恶意软件",
		"MALWARE_DOWNLOAD":   "恶意软件下载，尝试在目标系统中植入恶意软件",
		"ddos":               "分布式拒绝服务攻击，通过大量请求使服务不可用",
		"DDoS":               "分布式拒绝服务攻击，通过大量请求使服务不可用",
		"telnet_brute_force": "Telnet暴力破解攻击，尝试获取Telnet访问权限",
		"TELNET_BRUTE_FORCE": "Telnet暴力破解攻击，尝试获取Telnet访问权限",
		"ftp_brute_force":    "FTP暴力破解攻击，尝试获取FTP访问权限",
		"FTP_BRUTE_FORCE":    "FTP暴力破解攻击，尝试获取FTP访问权限",
		"http_flood":         "HTTP洪水攻击，通过大量HTTP请求消耗服务器资源",
		"HTTP_FLOOD":         "HTTP洪水攻击，通过大量HTTP请求消耗服务器资源",
	}

	if description, exists := descriptionMap[attackType]; exists {
		return description
	}
	return "未知攻击类型"
}
