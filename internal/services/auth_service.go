package services

import (
	"errors"
	"fmt"
	"time"

	"honeypot-admin/internal/database"
	"honeypot-admin/internal/models"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// JWT Claims 结构体
type JWTClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// 错误定义
var (
	ErrUserNotFound     = errors.New("user not found")
	ErrInvalidPassword  = errors.New("invalid password")
	ErrUserInactive     = errors.New("user is inactive")
	ErrUsernameExists   = errors.New("username already exists")
	ErrEmailExists      = errors.New("email already exists")
	ErrInvalidRole      = errors.New("invalid role")
	ErrWeakPassword     = errors.New("password is too weak")
	ErrInvalidToken     = errors.New("invalid token")
	ErrTokenExpired     = errors.New("token expired")
	ErrPermissionDenied = errors.New("permission denied")
)

// AuthService 认证服务
type AuthService struct {
	db        *gorm.DB
	jwtSecret string
	jwtExpiry time.Duration
}

// NewAuthService 创建认证服务
func NewAuthService(jwtSecret string, jwtExpiry time.Duration) *AuthService {
	return &AuthService{
		db:        database.GetDB(),
		jwtSecret: jwtSecret,
		jwtExpiry: jwtExpiry,
	}
}

// Login 用户登录
func (s *AuthService) Login(req *models.UserLoginRequest) (*models.UserLoginResponse, error) {
	// 查找用户
	var user models.User
	if err := s.db.Where("username = ?", req.Username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	// 检查用户状态
	if !user.IsActive() {
		return nil, ErrUserInactive
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, ErrInvalidPassword
	}

	// 生成JWT Token
	token, err := s.generateJWT(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.db.Save(&user)

	// 返回登录响应
	return &models.UserLoginResponse{
		Token:     token,
		ExpiresIn: int(s.jwtExpiry.Seconds()),
		UserInfo:  user.ToUserInfo(),
	}, nil
}

// Register 用户注册
func (s *AuthService) Register(req *models.UserRegisterRequest) (*models.User, error) {
	// 验证用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, ErrUsernameExists
	}

	// 验证邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, ErrEmailExists
	}

	// 验证密码强度
	if err := s.validatePassword(req.Password); err != nil {
		return nil, err
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 设置默认角色
	role := models.RoleObserver
	if req.Role != "" {
		role = models.UserRole(req.Role)
	}

	// 创建用户
	user := &models.User{
		Username: req.Username,
		Email:    req.Email,
		Password: string(hashedPassword),
		Role:     role,
		Status:   models.StatusActive,
	}

	// 保存到数据库
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// CreateUser 创建用户
func (s *AuthService) CreateUser(req *models.UserCreateRequest) (*models.User, error) {
	// 验证用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, ErrUsernameExists
	}

	// 验证邮箱是否已存在（如果提供）
	if req.Email != "" {
		if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
			return nil, ErrEmailExists
		}
	}

	// 验证密码强度
	if err := s.validatePassword(req.Password); err != nil {
		return nil, err
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 创建用户
	user := &models.User{
		Username: req.Username,
		Password: string(hashedPassword),
		Email:    req.Email,
		Role:     req.Role,
		Status:   models.StatusActive,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	return user, nil
}

// GetUserByID 根据ID获取用户
func (s *AuthService) GetUserByID(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *AuthService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// UpdateUser 更新用户
func (s *AuthService) UpdateUser(id uint, req *models.UserUpdateRequest) (*models.User, error) {
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrUserNotFound
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Email != "" {
		// 检查邮箱是否已被其他用户使用
		var existingUser models.User
		if err := s.db.Where("email = ? AND id != ?", req.Email, id).First(&existingUser).Error; err == nil {
			return nil, ErrEmailExists
		}
		updates["email"] = req.Email
	}
	if req.Role != "" {
		updates["role"] = req.Role
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&user).Updates(updates).Error; err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}
	}

	return &user, nil
}

// ChangePassword 修改密码
func (s *AuthService) ChangePassword(userID uint, req *models.PasswordChangeRequest) error {
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ErrUserNotFound
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.OldPassword)); err != nil {
		return ErrInvalidPassword
	}

	// 验证新密码强度
	if err := s.validatePassword(req.NewPassword); err != nil {
		return err
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// DeleteUser 删除用户
func (s *AuthService) DeleteUser(id uint) error {
	result := s.db.Delete(&models.User{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrUserNotFound
	}
	return nil
}

// ListUsers 获取用户列表
func (s *AuthService) ListUsers(page, size int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	// 获取总数
	if err := s.db.Model(&models.User{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := s.db.Order("created_at DESC").Offset(offset).Limit(size).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	return users, total, nil
}

// ValidateToken 验证JWT Token并返回用户信息
func (s *AuthService) ValidateToken(tokenString string) (*models.User, error) {
	// 验证JWT Token
	claims, err := s.validateJWTToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 获取用户信息
	return s.GetUserByID(claims.UserID)
}

// validateJWTToken 验证JWT Token并返回Claims
func (s *AuthService) validateJWTToken(tokenString string) (*JWTClaims, error) {
	// 解析Token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// 验证Claims
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, ErrInvalidToken
}

// generateJWT 生成JWT Token
func (s *AuthService) generateJWT(user *models.User) (string, error) {
	// 创建JWT Claims
	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.jwtExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "honeypot-admin",
			Subject:   fmt.Sprintf("user:%d", user.ID),
		},
	}

	// 创建Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名Token
	tokenString, err := token.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// parseUserIDFromToken 从Token解析用户ID (保持向后兼容)
func (s *AuthService) parseUserIDFromToken(token string) uint {
	claims, err := s.validateJWTToken(token)
	if err != nil {
		return 0
	}
	return claims.UserID
}

// validatePassword 验证密码强度
func (s *AuthService) validatePassword(password string) error {
	if len(password) < 6 {
		return ErrWeakPassword
	}

	// 可以添加更多密码强度检查
	// 如：包含大小写字母、数字、特殊字符等

	return nil
}

// GetUserStats 获取用户统计信息
func (s *AuthService) GetUserStats() map[string]interface{} {
	var stats struct {
		Total          int64 `json:"total"`
		Active         int64 `json:"active"`
		Inactive       int64 `json:"inactive"`
		Administrators int64 `json:"administrators"`
		Operators      int64 `json:"operators"`
		Observers      int64 `json:"observers"`
	}

	// 总用户数
	s.db.Model(&models.User{}).Count(&stats.Total)

	// 活跃用户数
	s.db.Model(&models.User{}).Where("status = ?", models.StatusActive).Count(&stats.Active)

	// 非活跃用户数
	s.db.Model(&models.User{}).Where("status = ?", models.StatusInactive).Count(&stats.Inactive)

	// 按角色统计
	s.db.Model(&models.User{}).Where("role = ?", models.RoleAdministrator).Count(&stats.Administrators)
	s.db.Model(&models.User{}).Where("role = ?", models.RoleOperator).Count(&stats.Operators)
	s.db.Model(&models.User{}).Where("role = ?", models.RoleObserver).Count(&stats.Observers)

	return map[string]interface{}{
		"total":          stats.Total,
		"active":         stats.Active,
		"inactive":       stats.Inactive,
		"administrators": stats.Administrators,
		"operators":      stats.Operators,
		"observers":      stats.Observers,
	}
}
