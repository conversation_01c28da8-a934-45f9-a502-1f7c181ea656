package services

import (
	"fmt"
	"time"

	"honeypot-admin/internal/database"
	"honeypot-admin/internal/models"

	"gorm.io/gorm"
)

// DashboardService 仪表板服务
type DashboardService struct {
	db *gorm.DB
}

// NewDashboardService 创建仪表板服务
func NewDashboardService() *DashboardService {
	return &DashboardService{
		db: database.GetDB(),
	}
}

// GetOverviewStats 获取概览统计信息
func (s *DashboardService) GetOverviewStats() (*models.DashboardOverview, error) {
	overview := &models.DashboardOverview{}

	// 节点统计
	nodeStats, err := s.getNodeStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get node stats: %w", err)
	}
	overview.NodeStats = nodeStats

	// 攻击统计
	attackStats, err := s.getAttackStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get attack stats: %w", err)
	}
	overview.AttackStats = attackStats

	// 模板统计
	templateStats, err := s.getTemplateStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get template stats: %w", err)
	}
	overview.TemplateStats = templateStats

	// 部署统计
	deploymentStats, err := s.getDeploymentStats()
	if err != nil {
		return nil, fmt.Errorf("failed to get deployment stats: %w", err)
	}
	overview.DeploymentStats = deploymentStats

	// 系统状态
	systemStatus, err := s.getSystemStatus()
	if err != nil {
		return nil, fmt.Errorf("failed to get system status: %w", err)
	}
	overview.SystemStatus = systemStatus

	return overview, nil
}

// GetAttackTrends 获取攻击趋势数据
func (s *DashboardService) GetAttackTrends(days int) (*models.AttackTrendsResponse, error) {
	if days <= 0 || days > 365 {
		days = 30 // 默认30天
	}

	startDate := time.Now().AddDate(0, 0, -days)

	// 每日攻击统计
	var dailyStats []models.DailyAttackStat
	query := `
		SELECT 
			DATE(created_at) as date,
			COUNT(*) as attack_count,
			COUNT(DISTINCT source_ip) as unique_ips,
			COUNT(DISTINCT node_id) as active_nodes
		FROM attack_events 
		WHERE created_at >= ? 
		GROUP BY DATE(created_at) 
		ORDER BY date
	`

	if err := s.db.Raw(query, startDate).Scan(&dailyStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get daily attack stats: %w", err)
	}

	// 热门攻击类型
	var topAttackTypes []models.AttackTypeCount
	typeQuery := `
		SELECT 
			attack_type,
			COUNT(*) as count
		FROM attack_events 
		WHERE created_at >= ? AND attack_type != ''
		GROUP BY attack_type 
		ORDER BY count DESC 
		LIMIT 10
	`

	if err := s.db.Raw(typeQuery, startDate).Scan(&topAttackTypes).Error; err != nil {
		return nil, fmt.Errorf("failed to get top attack types: %w", err)
	}

	// 热门源IP
	var topSourceIPs []models.SourceIPCount
	ipQuery := `
		SELECT 
			source_ip,
			COUNT(*) as count
		FROM attack_events 
		WHERE created_at >= ?
		GROUP BY source_ip 
		ORDER BY count DESC 
		LIMIT 10
	`

	if err := s.db.Raw(ipQuery, startDate).Scan(&topSourceIPs).Error; err != nil {
		return nil, fmt.Errorf("failed to get top source IPs: %w", err)
	}

	return &models.AttackTrendsResponse{
		Period:         fmt.Sprintf("Last %d days", days),
		DailyStats:     dailyStats,
		TopAttackTypes: topAttackTypes,
		TopSourceIPs:   topSourceIPs,
	}, nil
}

// GetNodeStatusOverview 获取节点状态概览
func (s *DashboardService) GetNodeStatusOverview() (*models.NodeStatusOverview, error) {
	overview := &models.NodeStatusOverview{}

	// 节点状态分布
	var statusStats []models.NodeStatusStat
	statusQuery := `
		SELECT 
			status,
			COUNT(*) as count
		FROM nodes 
		GROUP BY status
	`

	if err := s.db.Raw(statusQuery).Scan(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get node status stats: %w", err)
	}
	overview.StatusStats = statusStats

	// 区域分布
	var regionStats []models.NodeRegionStat
	regionQuery := `
		SELECT 
			region,
			COUNT(*) as count,
			SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online_count
		FROM nodes 
		GROUP BY region
	`

	if err := s.db.Raw(regionQuery).Scan(&regionStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get node region stats: %w", err)
	}
	overview.RegionStats = regionStats

	// 最近活跃的节点
	var recentActiveNodes []models.RecentActiveNode
	activeQuery := `
		SELECT 
			id,
			name,
			ip,
			status,
			last_heartbeat_at
		FROM nodes 
		WHERE last_heartbeat_at IS NOT NULL
		ORDER BY last_heartbeat_at DESC 
		LIMIT 10
	`

	if err := s.db.Raw(activeQuery).Scan(&recentActiveNodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent active nodes: %w", err)
	}
	overview.RecentActiveNodes = recentActiveNodes

	return overview, nil
}

// GetSecurityAlerts 获取安全告警
func (s *DashboardService) GetSecurityAlerts(limit int) (*models.SecurityAlertsResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 高危攻击事件
	var criticalAttacks []models.CriticalAttackAlert
	criticalQuery := `
		SELECT 
			id,
			node_id,
			source_ip,
			target_port,
			attack_type,
			severity,
			created_at
		FROM attack_events 
		WHERE severity = 'critical' 
		ORDER BY created_at DESC 
		LIMIT ?
	`

	if err := s.db.Raw(criticalQuery, limit).Scan(&criticalAttacks).Error; err != nil {
		return nil, fmt.Errorf("failed to get critical attacks: %w", err)
	}

	// 异常IP活动
	var suspiciousIPs []models.SuspiciousIPAlert
	suspiciousQuery := `
		SELECT 
			source_ip,
			COUNT(*) as attack_count,
			COUNT(DISTINCT target_port) as target_ports,
			COUNT(DISTINCT attack_type) as attack_types,
			MAX(created_at) as last_attack
		FROM attack_events 
		WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
		GROUP BY source_ip 
		HAVING attack_count > 10
		ORDER BY attack_count DESC 
		LIMIT ?
	`

	if err := s.db.Raw(suspiciousQuery, limit).Scan(&suspiciousIPs).Error; err != nil {
		return nil, fmt.Errorf("failed to get suspicious IPs: %w", err)
	}

	// 离线节点告警
	var offlineNodes []models.OfflineNodeAlert
	offlineQuery := `
		SELECT 
			id,
			name,
			ip,
			region,
			last_heartbeat_at
		FROM nodes 
		WHERE status = 'offline' 
		ORDER BY last_heartbeat_at DESC 
		LIMIT ?
	`

	if err := s.db.Raw(offlineQuery, limit).Scan(&offlineNodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get offline nodes: %w", err)
	}

	return &models.SecurityAlertsResponse{
		CriticalAttacks: criticalAttacks,
		SuspiciousIPs:   suspiciousIPs,
		OfflineNodes:    offlineNodes,
		GeneratedAt:     time.Now(),
	}, nil
}

// GetPerformanceMetrics 获取性能指标
func (s *DashboardService) GetPerformanceMetrics() (*models.PerformanceMetrics, error) {
	metrics := &models.PerformanceMetrics{}

	// 数据库性能
	dbStats := database.GetConnectionStats()
	metrics.DatabaseStats = dbStats

	// 攻击数据处理速率
	var processingStats models.ProcessingStats

	// 最近1小时的攻击数量
	oneHourAgo := time.Now().Add(-time.Hour)
	if err := s.db.Model(&models.AttackEvent{}).
		Where("created_at >= ?", oneHourAgo).
		Count(&processingStats.AttacksLastHour).Error; err != nil {
		return nil, fmt.Errorf("failed to get attacks last hour: %w", err)
	}

	// 最近24小时的攻击数量
	twentyFourHoursAgo := time.Now().Add(-24 * time.Hour)
	if err := s.db.Model(&models.AttackEvent{}).
		Where("created_at >= ?", twentyFourHoursAgo).
		Count(&processingStats.AttacksLast24Hours).Error; err != nil {
		return nil, fmt.Errorf("failed to get attacks last 24 hours: %w", err)
	}

	// 计算处理速率
	processingStats.AttacksPerHour = float64(processingStats.AttacksLast24Hours) / 24.0
	processingStats.AttacksPerMinute = float64(processingStats.AttacksLastHour) / 60.0

	metrics.ProcessingStats = processingStats

	// 存储使用情况
	var storageStats models.StorageStats

	// 获取表大小信息（MySQL特定）
	var tableSize struct {
		DataLength  int64 `json:"data_length"`
		IndexLength int64 `json:"index_length"`
	}

	sizeQuery := `
		SELECT 
			SUM(data_length) as data_length,
			SUM(index_length) as index_length
		FROM information_schema.tables 
		WHERE table_schema = DATABASE()
	`

	if err := s.db.Raw(sizeQuery).Scan(&tableSize).Error; err != nil {
		// 如果查询失败，使用默认值
		storageStats.TotalSize = 0
		storageStats.DataSize = 0
		storageStats.IndexSize = 0
	} else {
		storageStats.TotalSize = tableSize.DataLength + tableSize.IndexLength
		storageStats.DataSize = tableSize.DataLength
		storageStats.IndexSize = tableSize.IndexLength
	}

	metrics.StorageStats = storageStats

	return metrics, nil
}

// 辅助方法

func (s *DashboardService) getNodeStats() (models.NodeStats, error) {
	var stats models.NodeStats

	// 总节点数
	var totalCount int64
	s.db.Model(&models.Node{}).Count(&totalCount)
	stats.Total = int(totalCount)

	// 在线节点数
	var onlineCount int64
	s.db.Model(&models.Node{}).Where("status = ?", "online").Count(&onlineCount)
	stats.Online = int(onlineCount)

	// 离线节点数
	var offlineCount int64
	s.db.Model(&models.Node{}).Where("status = ?", "offline").Count(&offlineCount)
	stats.Offline = int(offlineCount)

	// 错误节点数
	var errorCount int64
	s.db.Model(&models.Node{}).Where("status = ?", "error").Count(&errorCount)
	stats.Error = int(errorCount)

	return stats, nil
}

func (s *DashboardService) getAttackStats() (models.AttackStats, error) {
	var stats models.AttackStats

	// 总攻击数
	s.db.Model(&models.AttackEvent{}).Count(&stats.TotalAttacks)

	// 今日攻击数
	today := time.Now().Format("2006-01-02")
	s.db.Model(&models.AttackEvent{}).Where("DATE(created_at) = ?", today).Count(&stats.TodayAttacks)

	// 唯一IP数
	s.db.Model(&models.AttackEvent{}).Distinct("source_ip").Count(&stats.UniqueIPs)

	// 被阻止的攻击数
	s.db.Model(&models.AttackEvent{}).Where("blocked = ?", true).Count(&stats.BlockedAttacks)

	return stats, nil
}

func (s *DashboardService) getTemplateStats() (models.TemplateStats, error) {
	var stats models.TemplateStats

	// 总模板数
	var totalCount int64
	s.db.Model(&models.Template{}).Count(&totalCount)
	stats.Total = int(totalCount)

	// 活跃模板数
	var activeCount int64
	s.db.Model(&models.Template{}).Where("active = ?", true).Count(&activeCount)
	stats.Active = int(activeCount)

	// 非活跃模板数
	stats.Inactive = stats.Total - stats.Active

	// 已部署模板数
	stats.Deployed = stats.Active

	return stats, nil
}

func (s *DashboardService) getDeploymentStats() (models.DeploymentStats, error) {
	var stats models.DeploymentStats

	// 总部署数
	var totalCount int64
	s.db.Model(&models.ServiceDeployment{}).Count(&totalCount)
	stats.Total = int(totalCount)

	// 运行中部署数
	var runningCount int64
	s.db.Model(&models.ServiceDeployment{}).Where("status = ?", "running").Count(&runningCount)
	stats.Running = int(runningCount)

	// 已停止部署数
	var stoppedCount int64
	s.db.Model(&models.ServiceDeployment{}).Where("status = ?", "stopped").Count(&stoppedCount)
	stats.Stopped = int(stoppedCount)

	// 失败部署数
	var failedCount int64
	s.db.Model(&models.ServiceDeployment{}).Where("status IN ?", []string{"failed", "error"}).Count(&failedCount)
	stats.Failed = int(failedCount)

	return stats, nil
}

func (s *DashboardService) getSystemStatus() (models.SystemStatus, error) {
	status := models.SystemStatus{
		Status:    "healthy",
		Uptime:    time.Since(time.Now().Add(-24 * time.Hour)).String(), // 简化实现
		Version:   "1.0.0",
		UpdatedAt: time.Now(),
	}

	// 检查数据库连接
	if err := database.HealthCheck(); err != nil {
		status.Status = "unhealthy"
		status.Issues = append(status.Issues, "数据库连接异常")
	}

	// 检查离线节点数量
	var offlineCount int64
	s.db.Model(&models.Node{}).Where("status = ?", "offline").Count(&offlineCount)
	if offlineCount > 0 {
		status.Issues = append(status.Issues, fmt.Sprintf("%d个节点离线", offlineCount))
	}

	return status, nil
}

// getProcessingStats 获取处理统计
func (s *DashboardService) getProcessingStats() models.ProcessingStats {
	var stats models.ProcessingStats

	// 获取最近一小时的攻击数
	oneHourAgo := time.Now().Add(-time.Hour)
	s.db.Model(&models.AttackEvent{}).Where("created_at >= ?", oneHourAgo).Count(&stats.AttacksLastHour)

	// 获取最近24小时的攻击数
	twentyFourHoursAgo := time.Now().Add(-24 * time.Hour)
	s.db.Model(&models.AttackEvent{}).Where("created_at >= ?", twentyFourHoursAgo).Count(&stats.AttacksLast24Hours)

	// 计算每小时攻击数
	if stats.AttacksLast24Hours > 0 {
		stats.AttacksPerHour = float64(stats.AttacksLast24Hours) / 24.0
	}

	// 计算每分钟攻击数
	if stats.AttacksLastHour > 0 {
		stats.AttacksPerMinute = float64(stats.AttacksLastHour) / 60.0
	}

	// 计算每秒事件数
	if stats.AttacksLastHour > 0 {
		stats.EventsPerSec = float64(stats.AttacksLastHour) / 3600.0
	}

	// 设置其他统计信息
	stats.ProcessingDelay = 0.5 // 平均处理延迟（秒）
	stats.QueueSize = 0         // 当前队列大小
	stats.ErrorRate = 0.01      // 错误率（1%）
	stats.Throughput = stats.EventsPerSec

	return stats
}
