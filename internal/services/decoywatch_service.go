package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"honeypot-admin/internal/config"
)

// DecoyWatchService DecoyWatch情报收集系统客户端服务
type DecoyWatchService struct {
	config     *config.Config
	httpClient *http.Client
	baseURL    string
	token      string
}

// NewDecoyWatchService 创建DecoyWatch服务实例
func NewDecoyWatchService(cfg *config.Config) *DecoyWatchService {
	return &DecoyWatchService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL: cfg.External.DecoyWatchURL,
		token:   cfg.External.DecoyWatchToken,
	}
}

// AttackEventRequest 攻击事件请求结构
type AttackEventRequest struct {
	NodeID      string    `json:"node_id"`
	Timestamp   time.Time `json:"timestamp"`
	SourceIP    string    `json:"source_ip"`
	SourcePort  int       `json:"source_port"`
	TargetPort  int       `json:"target_port"`
	Protocol    string    `json:"protocol"`
	AttackType  string    `json:"attack_type"`
	Payload     string    `json:"payload"`
	SessionID   string    `json:"session_id"`
	Severity    string    `json:"severity"`
	Blocked     bool      `json:"blocked"`
	Description string    `json:"description"`
}

// AttackTypeCount 攻击类型计数
type AttackTypeCount struct {
	AttackType string `json:"attack_type"`
	Count      int64  `json:"count"`
}

// TargetPortCount 目标端口计数
type TargetPortCount struct {
	TargetPort int   `json:"target_port"`
	Count      int64 `json:"count"`
}

// HourlyAttackCount 每小时攻击计数
type HourlyAttackCount struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}

// AttackStatisticsResponse 攻击统计响应结构
type AttackStatisticsResponse struct {
	TotalAttacks   int64               `json:"total_attacks"`
	TodayAttacks   int64               `json:"today_attacks"`
	UniqueIPs      int64               `json:"unique_ips"`
	TopAttackTypes []AttackTypeCount   `json:"top_attack_types"`
	TopSourceIPs   []SourceIPCount     `json:"top_source_ips"`
	TopTargetPorts []TargetPortCount   `json:"top_target_ports"`
	HourlyStats    []HourlyAttackCount `json:"hourly_stats"`
}

// DecoyWatchResponse DecoyWatch API响应结构
type DecoyWatchResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
	Error   string      `json:"error,omitempty"`
}

// SendAttackEvent 发送单个攻击事件到DecoyWatch
func (s *DecoyWatchService) SendAttackEvent(ctx context.Context, event *AttackEventRequest) error {
	url := fmt.Sprintf("%s/api/v1/intelligence/attacks", s.baseURL)

	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal attack event: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.token)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("DecoyWatch API error: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// SendAttackEventsBatch 批量发送攻击事件到DecoyWatch
func (s *DecoyWatchService) SendAttackEventsBatch(ctx context.Context, events []*AttackEventRequest) error {
	url := fmt.Sprintf("%s/api/v1/intelligence/attacks/batch", s.baseURL)

	data, err := json.Marshal(map[string]interface{}{
		"events": events,
	})
	if err != nil {
		return fmt.Errorf("failed to marshal attack events: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.token)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("DecoyWatch API error: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// GetAttackStatistics 获取攻击统计数据
func (s *DecoyWatchService) GetAttackStatistics(ctx context.Context, nodeID string, startTime, endTime *time.Time) (*AttackStatisticsResponse, error) {
	url := fmt.Sprintf("%s/api/v1/intelligence/statistics/attacks", s.baseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 添加查询参数
	q := req.URL.Query()
	if nodeID != "" {
		q.Add("node_id", nodeID)
	}
	if startTime != nil {
		q.Add("start_time", startTime.Format(time.RFC3339))
	}
	if endTime != nil {
		q.Add("end_time", endTime.Format(time.RFC3339))
	}
	req.URL.RawQuery = q.Encode()

	req.Header.Set("Authorization", "Bearer "+s.token)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("DecoyWatch API error: %d - %s", resp.StatusCode, string(body))
	}

	var response DecoyWatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("DecoyWatch API error: %s", response.Error)
	}

	// 将data转换为AttackStatisticsResponse
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response data: %w", err)
	}

	var stats AttackStatisticsResponse
	if err := json.Unmarshal(dataBytes, &stats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal statistics: %w", err)
	}

	return &stats, nil
}

// SourceIPCount IP统计数据结构
type SourceIPCount struct {
	SourceIP string `json:"source_ip"`
	Count    int64  `json:"count"`
	Country  string `json:"country,omitempty"`
}

// GetIPStatistics 获取IP统计数据
func (s *DecoyWatchService) GetIPStatistics(ctx context.Context, limit int) ([]SourceIPCount, error) {
	url := fmt.Sprintf("%s/api/v1/intelligence/statistics/ips", s.baseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	if limit > 0 {
		q := req.URL.Query()
		q.Add("limit", fmt.Sprintf("%d", limit))
		req.URL.RawQuery = q.Encode()
	}

	req.Header.Set("Authorization", "Bearer "+s.token)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("DecoyWatch API error: %d - %s", resp.StatusCode, string(body))
	}

	var response DecoyWatchResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("DecoyWatch API error: %s", response.Error)
	}

	// 将data转换为[]SourceIPCount
	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal response data: %w", err)
	}

	var ipStats []SourceIPCount
	if err := json.Unmarshal(dataBytes, &ipStats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal IP statistics: %w", err)
	}

	return ipStats, nil
}

// HealthCheck 检查DecoyWatch服务健康状态
func (s *DecoyWatchService) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", s.baseURL)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("DecoyWatch health check failed: %d", resp.StatusCode)
	}

	return nil
}

// IsEnabled 检查DecoyWatch集成是否启用
func (s *DecoyWatchService) IsEnabled() bool {
	return s.baseURL != "" && s.token != ""
}
