package services

import (
	"errors"
	"fmt"
	"time"

	"honeypot-admin/internal/models"

	"gorm.io/gorm"
)

// 错误定义
var (
	ErrDeploymentNotFound = errors.New("deployment not found")
	ErrDeploymentExists   = errors.New("deployment already exists")
	ErrInvalidAction      = errors.New("invalid deployment action")
	ErrNodeNotConnected   = errors.New("node not connected")
)

// DeploymentService 部署服务
type DeploymentService struct {
	db *gorm.DB
}

// NewDeploymentService 创建部署服务
func NewDeploymentService(db *gorm.DB) *DeploymentService {
	return &DeploymentService{
		db: db,
	}
}

// CreateDeployment 创建部署记录
func (s *DeploymentService) CreateDeployment(deployment *models.ServiceDeployment) error {
	// 检查是否已存在相同的部署
	var existing models.ServiceDeployment
	err := s.db.Where("node_id = ? AND template_id = ? AND name = ?",
		deployment.NodeID, deployment.TemplateID, deployment.Name).First(&existing).Error

	if err == nil {
		return ErrDeploymentExists
	}

	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing deployment: %w", err)
	}

	// 创建部署记录
	if err := s.db.Create(deployment).Error; err != nil {
		return fmt.Errorf("failed to create deployment: %w", err)
	}

	return nil
}

// GetDeployment 获取部署信息
func (s *DeploymentService) GetDeployment(id uint) (*models.ServiceDeployment, error) {
	var deployment models.ServiceDeployment
	if err := s.db.First(&deployment, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrDeploymentNotFound
		}
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	return &deployment, nil
}

// GetDeploymentByContainerID 根据容器ID获取部署信息
func (s *DeploymentService) GetDeploymentByContainerID(containerID string) (*models.ServiceDeployment, error) {
	var deployment models.ServiceDeployment
	if err := s.db.Where("container_id = ?", containerID).First(&deployment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrDeploymentNotFound
		}
		return nil, fmt.Errorf("failed to get deployment: %w", err)
	}

	return &deployment, nil
}

// ListDeployments 获取部署列表
func (s *DeploymentService) ListDeployments(req *models.DeploymentListRequest) ([]models.ServiceDeployment, int64, error) {
	var deployments []models.ServiceDeployment
	var total int64

	// 构建查询
	query := s.db.Model(&models.ServiceDeployment{})

	// 添加过滤条件
	if req.NodeID != "" {
		query = query.Where("node_id = ?", req.NodeID)
	}
	if req.TemplateID != 0 {
		query = query.Where("template_id = ?", req.TemplateID)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+req.Keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count deployments: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.Size
	if err := query.Offset(offset).Limit(req.Size).
		Order("created_at DESC").Find(&deployments).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list deployments: %w", err)
	}

	return deployments, total, nil
}

// UpdateDeploymentStatus 更新部署状态
func (s *DeploymentService) UpdateDeploymentStatus(id uint, status string, containerID string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if containerID != "" {
		updates["container_id"] = containerID
	}

	if status == "running" {
		updates["started_at"] = time.Now()
	} else if status == "stopped" || status == "failed" {
		updates["stopped_at"] = time.Now()
	}

	if err := s.db.Model(&models.ServiceDeployment{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update deployment status: %w", err)
	}

	return nil
}

// DeleteDeployment 删除部署
func (s *DeploymentService) DeleteDeployment(id uint) error {
	result := s.db.Delete(&models.ServiceDeployment{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete deployment: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrDeploymentNotFound
	}

	return nil
}

// GetNodeDeployments 获取节点的所有部署
func (s *DeploymentService) GetNodeDeployments(nodeID string) ([]models.ServiceDeployment, error) {
	var deployments []models.ServiceDeployment
	if err := s.db.Where("node_id = ?", nodeID).Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to get node deployments: %w", err)
	}

	return deployments, nil
}

// GetTemplateDeployments 获取模板的所有部署
func (s *DeploymentService) GetTemplateDeployments(templateID uint) ([]models.ServiceDeployment, error) {
	var deployments []models.ServiceDeployment
	if err := s.db.Where("template_id = ?", templateID).Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to get template deployments: %w", err)
	}

	return deployments, nil
}

// CreateDeploymentCommand 创建部署命令
func (s *DeploymentService) CreateDeploymentCommand(command *models.DeploymentCommand) error {
	if err := s.db.Create(command).Error; err != nil {
		return fmt.Errorf("failed to create deployment command: %w", err)
	}

	return nil
}

// UpdateDeploymentCommand 更新部署命令状态
func (s *DeploymentService) UpdateDeploymentCommand(id uint, status, result, errorMsg string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	if result != "" {
		updates["result"] = result
	}
	if errorMsg != "" {
		updates["error"] = errorMsg
	}

	if status == "executing" {
		updates["executed_at"] = time.Now()
	} else if status == "completed" || status == "failed" {
		updates["completed_at"] = time.Now()
	}

	if err := s.db.Model(&models.DeploymentCommand{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update deployment command: %w", err)
	}

	return nil
}

// GetDeploymentStatistics 获取部署统计信息
func (s *DeploymentService) GetDeploymentStatistics() (*models.DeploymentStatistics, error) {
	var stats models.DeploymentStatistics

	// 总部署数
	if err := s.db.Model(&models.ServiceDeployment{}).Count(&stats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count total deployments: %w", err)
	}

	// 运行中的部署数
	if err := s.db.Model(&models.ServiceDeployment{}).
		Where("status = ?", "running").Count(&stats.Running).Error; err != nil {
		return nil, fmt.Errorf("failed to count running deployments: %w", err)
	}

	// 停止的部署数
	if err := s.db.Model(&models.ServiceDeployment{}).
		Where("status = ?", "stopped").Count(&stats.Stopped).Error; err != nil {
		return nil, fmt.Errorf("failed to count stopped deployments: %w", err)
	}

	// 失败的部署数
	if err := s.db.Model(&models.ServiceDeployment{}).
		Where("status = ?", "failed").Count(&stats.Failed).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed deployments: %w", err)
	}

	return &stats, nil
}

// ValidateDeploymentAction 验证部署操作
func (s *DeploymentService) ValidateDeploymentAction(action string) error {
	validActions := []string{"deploy", "start", "stop", "restart", "update", "remove"}
	for _, validAction := range validActions {
		if action == validAction {
			return nil
		}
	}
	return ErrInvalidAction
}
