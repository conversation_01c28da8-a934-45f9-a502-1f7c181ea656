package services

import (
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"honeypot-admin/internal/database"
	"honeypot-admin/internal/models"

	"gorm.io/gorm"
)

// 错误定义
var (
	ErrNodeNotFound    = errors.New("node not found")
	ErrNodeExists      = errors.New("node already exists")
	ErrInvalidNodeData = errors.New("invalid node data")
	ErrTokenGeneration = errors.New("failed to generate token")
)

// NodeService 节点服务
type NodeService struct {
	db *gorm.DB
}

// NewNodeService 创建节点服务
func NewNodeService() *NodeService {
	return &NodeService{
		db: database.GetDB(),
	}
}

// RegisterNode 注册节点
func (s *NodeService) RegisterNode(req *models.NodeRegisterRequest) (*models.Node, error) {
	// 检查节点是否已存在
	var existingNode models.Node
	if err := s.db.Where("id = ?", req.ID).First(&existingNode).Error; err == nil {
		return nil, ErrNodeExists
	}

	// 生成认证Token
	token, err := s.generateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 创建节点
	node := &models.Node{
		ID:          req.ID,
		Name:        req.Name,
		IP:          req.IP,
		Token:       token,
		Status:      models.NodeStatusOffline,
		Region:      req.Region,
		Description: req.Description,
	}

	if err := s.db.Create(node).Error; err != nil {
		return nil, fmt.Errorf("failed to create node: %w", err)
	}

	return node, nil
}

// GetNode 获取节点信息
func (s *NodeService) GetNode(id string) (*models.Node, error) {
	var node models.Node
	if err := s.db.First(&node, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNodeNotFound
		}
		return nil, fmt.Errorf("failed to get node: %w", err)
	}
	return &node, nil
}

// UpdateNode 更新节点信息
func (s *NodeService) UpdateNode(id string, req *models.NodeUpdateRequest) (*models.Node, error) {
	var node models.Node
	if err := s.db.First(&node, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNodeNotFound
		}
		return nil, fmt.Errorf("failed to get node: %w", err)
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.IP != "" {
		updates["ip"] = req.IP
	}
	if req.Region != "" {
		updates["region"] = req.Region
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	if len(updates) > 0 {
		if err := s.db.Model(&node).Updates(updates).Error; err != nil {
			return nil, fmt.Errorf("failed to update node: %w", err)
		}
	}

	return &node, nil
}

// DeleteNode 删除节点
func (s *NodeService) DeleteNode(id string) error {
	result := s.db.Delete(&models.Node{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete node: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrNodeNotFound
	}
	return nil
}

// ListNodes 获取节点列表
func (s *NodeService) ListNodes(req *models.NodeListRequest) ([]*models.Node, int64, error) {
	query := s.db.Model(&models.Node{})

	// 应用过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Region != "" {
		query = query.Where("region = ?", req.Region)
	}
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR ip LIKE ? OR description LIKE ?",
			"%"+req.Keyword+"%", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count nodes: %w", err)
	}

	// 分页查询
	var nodes []*models.Node
	offset := (req.Page - 1) * req.Size
	if err := query.Order("created_at DESC").Offset(offset).Limit(req.Size).Find(&nodes).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list nodes: %w", err)
	}

	return nodes, total, nil
}

// UpdateNodeStatus 更新节点状态
func (s *NodeService) UpdateNodeStatus(id string, status models.NodeStatus) error {
	result := s.db.Model(&models.Node{}).Where("id = ?", id).Updates(map[string]interface{}{
		"status":            status,
		"last_heartbeat_at": time.Now(),
	})

	if result.Error != nil {
		return fmt.Errorf("failed to update node status: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrNodeNotFound
	}

	return nil
}

// UpdateNodeHeartbeat 更新节点心跳
func (s *NodeService) UpdateNodeHeartbeat(id string) error {
	result := s.db.Model(&models.Node{}).Where("id = ?", id).Update("last_heartbeat_at", time.Now())
	if result.Error != nil {
		return fmt.Errorf("failed to update heartbeat: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrNodeNotFound
	}
	return nil
}

// RegenerateToken 重新生成节点Token
func (s *NodeService) RegenerateToken(id string) (*models.Node, error) {
	var node models.Node
	if err := s.db.First(&node, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrNodeNotFound
		}
		return nil, fmt.Errorf("failed to get node: %w", err)
	}

	// 生成新Token
	token, err := s.generateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// 更新Token
	if err := s.db.Model(&node).Update("token", token).Error; err != nil {
		return nil, fmt.Errorf("failed to update token: %w", err)
	}

	node.Token = token
	return &node, nil
}

// GetNodesByStatus 根据状态获取节点列表
func (s *NodeService) GetNodesByStatus(status models.NodeStatus) ([]*models.Node, error) {
	var nodes []*models.Node
	if err := s.db.Where("status = ?", status).Find(&nodes).Error; err != nil {
		return nil, fmt.Errorf("failed to get nodes by status: %w", err)
	}
	return nodes, nil
}

// GetOnlineNodes 获取在线节点列表
func (s *NodeService) GetOnlineNodes() ([]*models.Node, error) {
	return s.GetNodesByStatus(models.NodeStatusOnline)
}

// GetOfflineNodes 获取离线节点列表
func (s *NodeService) GetOfflineNodes() ([]*models.Node, error) {
	return s.GetNodesByStatus(models.NodeStatusOffline)
}

// CheckNodeHealth 检查节点健康状态
func (s *NodeService) CheckNodeHealth() error {
	// 获取所有节点
	var nodes []*models.Node
	if err := s.db.Find(&nodes).Error; err != nil {
		return fmt.Errorf("failed to get nodes: %w", err)
	}

	now := time.Now()
	offlineThreshold := 5 * time.Minute // 5分钟无心跳认为离线

	for _, node := range nodes {
		if node.LastHeartbeatAt == nil {
			// 从未有心跳，设置为离线
			if node.Status != models.NodeStatusOffline {
				s.UpdateNodeStatus(node.ID, models.NodeStatusOffline)
			}
			continue
		}

		// 检查心跳时间
		if now.Sub(*node.LastHeartbeatAt) > offlineThreshold {
			// 超过阈值，设置为离线
			if node.Status != models.NodeStatusOffline {
				s.UpdateNodeStatus(node.ID, models.NodeStatusOffline)
			}
		}
	}

	return nil
}

// GetNodeStats 获取节点统计信息
func (s *NodeService) GetNodeStats() map[string]interface{} {
	var stats struct {
		Total   int64 `json:"total"`
		Online  int64 `json:"online"`
		Offline int64 `json:"offline"`
		Error   int64 `json:"error"`
	}

	// 总节点数
	s.db.Model(&models.Node{}).Count(&stats.Total)

	// 在线节点数
	s.db.Model(&models.Node{}).Where("status = ?", models.NodeStatusOnline).Count(&stats.Online)

	// 离线节点数
	s.db.Model(&models.Node{}).Where("status = ?", models.NodeStatusOffline).Count(&stats.Offline)

	// 错误节点数
	s.db.Model(&models.Node{}).Where("status = ?", models.NodeStatusError).Count(&stats.Error)

	// 按区域统计
	var regionStats []struct {
		Region string `json:"region"`
		Count  int64  `json:"count"`
	}
	s.db.Model(&models.Node{}).Select("region, COUNT(*) as count").Group("region").Scan(&regionStats)

	return map[string]interface{}{
		"total":        stats.Total,
		"online":       stats.Online,
		"offline":      stats.Offline,
		"error":        stats.Error,
		"region_stats": regionStats,
	}
}

// ValidateNodeToken 验证节点Token
func (s *NodeService) ValidateNodeToken(token string) (*models.Node, error) {
	var node models.Node
	if err := s.db.Where("token = ?", token).First(&node).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invalid token")
		}
		return nil, fmt.Errorf("failed to validate token: %w", err)
	}
	return &node, nil
}

// generateToken 生成随机Token
func (s *NodeService) generateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", ErrTokenGeneration
	}
	return hex.EncodeToString(bytes), nil
}

// GetNodeDeployments 获取节点的服务部署列表
func (s *NodeService) GetNodeDeployments(nodeID string) ([]*models.ServiceDeployment, error) {
	var deployments []*models.ServiceDeployment
	if err := s.db.Where("node_id = ?", nodeID).Preload("Template").Find(&deployments).Error; err != nil {
		return nil, fmt.Errorf("failed to get node deployments: %w", err)
	}
	return deployments, nil
}

// GetNodeMetrics 获取节点指标信息
func (s *NodeService) GetNodeMetrics(nodeID string) (map[string]interface{}, error) {
	// 这里应该从监控系统或节点直接获取指标
	// 简化实现，返回模拟数据
	metrics := map[string]interface{}{
		"cpu_usage":    "15.2%",
		"memory_usage": "45.8%",
		"disk_usage":   "23.1%",
		"network_in":   "1.2 MB/s",
		"network_out":  "0.8 MB/s",
		"uptime":       "72h 15m",
		"containers":   3,
		"last_update":  time.Now().Format(time.RFC3339),
	}

	return metrics, nil
}
