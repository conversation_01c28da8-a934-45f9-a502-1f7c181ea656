package services

import (
	"errors"
	"fmt"
	"time"

	"honeypot-admin/internal/models"

	"github.com/docker/docker/client"
	"gorm.io/gorm"
)

// TemplateService 模板服务
type TemplateService struct {
	db           *gorm.DB
	dockerClient *client.Client
}

// 错误定义
var (
	ErrTemplateNotFound = errors.New("template not found")
	ErrTemplateExists   = errors.New("template already exists")
	ErrDockerConnection = errors.New("docker connection failed")
	ErrImageNotFound    = errors.New("docker image not found")
)

// NewTemplateService 创建模板服务
func NewTemplateService(db *gorm.DB) *TemplateService {
	// 尝试创建Docker客户端
	dockerClient, err := client.NewClientWithOpts(client.FromEnv, client.WithAPIVersionNegotiation())
	if err != nil {
		// 如果Docker不可用，记录错误但不阻止服务启动
		fmt.Printf("Warning: Docker client initialization failed: %v\n", err)
		dockerClient = nil
	}

	return &TemplateService{
		db:           db,
		dockerClient: dockerClient,
	}
}

// CreateTemplate 创建模板
func (s *TemplateService) CreateTemplate(template *models.Template) error {
	// 检查模板名称是否已存在
	var existingTemplate models.Template
	if err := s.db.Where("name = ?", template.Name).First(&existingTemplate).Error; err == nil {
		return ErrTemplateExists
	}

	// 验证模板
	if err := s.ValidateTemplate(template); err != nil {
		return err
	}

	// 确保ID不为空
	if template.ID == "" {
		template.ID = fmt.Sprintf("tpl-%d", time.Now().Unix())
	}

	// 创建模板
	if err := s.db.Create(template).Error; err != nil {
		return fmt.Errorf("failed to create template: %w", err)
	}

	return nil
}

// GetTemplate 获取模板
func (s *TemplateService) GetTemplate(id string) (*models.Template, error) {
	var template models.Template
	if err := s.db.First(&template, "id = ?", id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrTemplateNotFound
		}
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	return &template, nil
}

// GetTemplates 获取模板列表
func (s *TemplateService) GetTemplates(page, pageSize int, search string) ([]*models.Template, int64, error) {
	var templates []*models.Template
	var total int64

	query := s.db.Model(&models.Template{})

	// 搜索条件
	if search != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count templates: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&templates).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get templates: %w", err)
	}

	return templates, total, nil
}

// UpdateTemplate 更新模板
func (s *TemplateService) UpdateTemplate(id string, updates map[string]interface{}) error {
	result := s.db.Model(&models.Template{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrTemplateNotFound
	}

	return nil
}

// DeleteTemplate 删除模板
func (s *TemplateService) DeleteTemplate(id string) error {
	result := s.db.Delete(&models.Template{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrTemplateNotFound
	}

	return nil
}

// GetTemplatesByType 根据类型获取模板
func (s *TemplateService) GetTemplatesByType(templateType string) ([]*models.Template, error) {
	var templates []*models.Template
	if err := s.db.Where("type = ? AND active = ?", templateType, true).Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to get templates by type: %w", err)
	}

	return templates, nil
}

// GetActiveTemplates 获取活跃模板
func (s *TemplateService) GetActiveTemplates() ([]*models.Template, error) {
	var templates []*models.Template
	if err := s.db.Where("active = ?", true).Find(&templates).Error; err != nil {
		return nil, fmt.Errorf("failed to get active templates: %w", err)
	}

	return templates, nil
}

// ActivateTemplate 激活模板
func (s *TemplateService) ActivateTemplate(id string) error {
	return s.UpdateTemplate(id, map[string]interface{}{"active": true})
}

// DeactivateTemplate 停用模板
func (s *TemplateService) DeactivateTemplate(id string) error {
	return s.UpdateTemplate(id, map[string]interface{}{"active": false})
}

// GetTemplateStats 获取模板统计
func (s *TemplateService) GetTemplateStats() (*models.TemplateStats, error) {
	var stats models.TemplateStats

	// 总数
	var totalCount int64
	if err := s.db.Model(&models.Template{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count total templates: %w", err)
	}
	stats.Total = int(totalCount)

	// 活跃数
	var activeCount int64
	if err := s.db.Model(&models.Template{}).Where("active = ?", true).Count(&activeCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count active templates: %w", err)
	}
	stats.Active = int(activeCount)

	// 非活跃数
	stats.Inactive = int(stats.Total) - stats.Active

	// 已部署数（简化实现）
	stats.Deployed = stats.Active

	return &stats, nil
}

// ValidateTemplate 验证模板
func (s *TemplateService) ValidateTemplate(template *models.Template) error {
	if template.Name == "" {
		return errors.New("template name is required")
	}

	if template.ImageName == "" {
		return errors.New("image name is required")
	}

	if template.Type == "" {
		return errors.New("template type is required")
	}

	return nil
}

// PullDockerImage 拉取Docker镜像
func (s *TemplateService) PullDockerImage(imageName string) error {
	if s.dockerClient == nil {
		return ErrDockerConnection
	}

	// 简化实现：记录拉取请求
	fmt.Printf("Pulling Docker image: %s\n", imageName)

	// 在实际实现中，这里应该调用Docker API拉取镜像
	// ctx := context.Background()
	// reader, err := s.dockerClient.ImagePull(ctx, imageName, image.PullOptions{})
	// if err != nil {
	//     return fmt.Errorf("failed to pull image %s: %w", imageName, err)
	// }
	// defer reader.Close()

	return nil
}
