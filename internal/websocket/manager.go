package websocket

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"honeypot-admin/internal/models"
	"honeypot-admin/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocketManager WebSocket连接管理器
type WebSocketManager struct {
	clients           map[string]*Client // 客户端连接映射 (nodeID -> Client)
	adminClients      map[string]*Client // 管理员连接映射 (userID -> Client)
	register          chan *Client       // 注册通道
	unregister        chan *Client       // 注销通道
	broadcast         chan []byte        // 广播通道
	mutex             sync.RWMutex       // 读写锁
	nodeService       *services.NodeService
	deploymentService *services.DeploymentService
}

// Client WebSocket客户端
type Client struct {
	ID       string          // 客户端ID (nodeID 或 userID)
	Type     ClientType      // 客户端类型
	Conn     *websocket.Conn // WebSocket连接
	Send     chan []byte     // 发送通道
	Manager  *WebSocketManager
	LastPing time.Time        // 最后ping时间
	IsActive bool             // 是否活跃
	UserInfo *models.UserInfo // 用户信息（管理员客户端）
	NodeInfo *models.Node     // 节点信息（节点客户端）
}

// ClientType 客户端类型
type ClientType string

const (
	ClientTypeNode  ClientType = "node"  // 节点客户端
	ClientTypeAdmin ClientType = "admin" // 管理员客户端
)

// Message WebSocket消息结构
type Message struct {
	Type      MessageType `json:"type"`
	From      string      `json:"from"`
	To        string      `json:"to,omitempty"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// MessageType 消息类型
type MessageType string

const (
	// 系统消息
	MessageTypeHeartbeat    MessageType = "heartbeat"
	MessageTypeAuth         MessageType = "auth"
	MessageTypeAuthResponse MessageType = "auth_response"
	MessageTypeError        MessageType = "error"

	// 节点管理消息
	MessageTypeNodeStatus   MessageType = "node_status"
	MessageTypeNodeCommand  MessageType = "node_command"
	MessageTypeNodeResponse MessageType = "node_response"

	// 部署管理消息
	MessageTypeDeployCommand  MessageType = "deploy_command"
	MessageTypeDeployStatus   MessageType = "deploy_status"
	MessageTypeDeployResponse MessageType = "deploy_response"

	// 数据同步消息
	MessageTypeAttackData MessageType = "attack_data"
	MessageTypeLogData    MessageType = "log_data"

	// 管理员通知消息
	MessageTypeNotification MessageType = "notification"
	MessageTypeBroadcast    MessageType = "broadcast"
)

// WebSocket升级器配置
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 在生产环境中应该检查Origin
		return true
	},
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(nodeService *services.NodeService, deploymentService *services.DeploymentService) *WebSocketManager {
	manager := &WebSocketManager{
		clients:           make(map[string]*Client),
		adminClients:      make(map[string]*Client),
		register:          make(chan *Client),
		unregister:        make(chan *Client),
		broadcast:         make(chan []byte),
		nodeService:       nodeService,
		deploymentService: deploymentService,
	}

	go manager.run()
	return manager
}

// run 运行WebSocket管理器
func (m *WebSocketManager) run() {
	ticker := time.NewTicker(30 * time.Second) // 30秒心跳检查
	defer ticker.Stop()

	for {
		select {
		case client := <-m.register:
			m.registerClient(client)

		case client := <-m.unregister:
			m.unregisterClient(client)

		case message := <-m.broadcast:
			m.broadcastMessage(message)

		case <-ticker.C:
			m.checkHeartbeat()
		}
	}
}

// registerClient 注册客户端
func (m *WebSocketManager) registerClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	switch client.Type {
	case ClientTypeNode:
		m.clients[client.ID] = client
		log.Printf("Node client registered: %s", client.ID)

		// 更新节点状态为在线
		if m.nodeService != nil {
			m.nodeService.UpdateNodeStatus(client.ID, models.NodeStatusOnline)
		}

	case ClientTypeAdmin:
		m.adminClients[client.ID] = client
		log.Printf("Admin client registered: %s", client.ID)
	}

	// 发送认证成功消息
	authResponse := Message{
		Type:      MessageTypeAuthResponse,
		From:      "server",
		Data:      map[string]interface{}{"status": "success", "message": "认证成功"},
		Timestamp: time.Now(),
	}

	if data, err := json.Marshal(authResponse); err == nil {
		select {
		case client.Send <- data:
		default:
			close(client.Send)
		}
	}
}

// unregisterClient 注销客户端
func (m *WebSocketManager) unregisterClient(client *Client) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	switch client.Type {
	case ClientTypeNode:
		if _, ok := m.clients[client.ID]; ok {
			delete(m.clients, client.ID)
			close(client.Send)
			log.Printf("Node client unregistered: %s", client.ID)

			// 更新节点状态为离线
			if m.nodeService != nil {
				m.nodeService.UpdateNodeStatus(client.ID, models.NodeStatusOffline)
			}
		}

	case ClientTypeAdmin:
		if _, ok := m.adminClients[client.ID]; ok {
			delete(m.adminClients, client.ID)
			close(client.Send)
			log.Printf("Admin client unregistered: %s", client.ID)
		}
	}
}

// broadcastMessage 广播消息
func (m *WebSocketManager) broadcastMessage(message []byte) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 广播给所有管理员客户端
	for _, client := range m.adminClients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(m.adminClients, client.ID)
		}
	}
}

// checkHeartbeat 检查心跳
func (m *WebSocketManager) checkHeartbeat() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	now := time.Now()
	timeout := 60 * time.Second // 60秒超时

	// 检查节点客户端
	for id, client := range m.clients {
		if now.Sub(client.LastPing) > timeout {
			log.Printf("Node client %s heartbeat timeout", id)
			client.Conn.Close()
			delete(m.clients, id)
			close(client.Send)

			// 更新节点状态
			if m.nodeService != nil {
				m.nodeService.UpdateNodeStatus(id, models.NodeStatusOffline)
			}
		}
	}

	// 检查管理员客户端
	for id, client := range m.adminClients {
		if now.Sub(client.LastPing) > timeout {
			log.Printf("Admin client %s heartbeat timeout", id)
			client.Conn.Close()
			delete(m.adminClients, id)
			close(client.Send)
		}
	}
}

// HandleNodeWebSocket 处理节点WebSocket连接
func (m *WebSocketManager) HandleNodeWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	// 等待认证消息
	var authMsg Message
	if err := conn.ReadJSON(&authMsg); err != nil {
		log.Printf("Failed to read auth message: %v", err)
		conn.Close()
		return
	}

	// 验证认证消息
	if authMsg.Type != MessageTypeAuth {
		conn.WriteJSON(Message{
			Type: MessageTypeError,
			Data: map[string]interface{}{"error": "Expected auth message"},
		})
		conn.Close()
		return
	}

	// 提取节点ID和Token
	authData, ok := authMsg.Data.(map[string]interface{})
	if !ok {
		conn.WriteJSON(Message{
			Type: MessageTypeError,
			Data: map[string]interface{}{"error": "Invalid auth data"},
		})
		conn.Close()
		return
	}

	nodeID, _ := authData["node_id"].(string)
	token, _ := authData["token"].(string)

	if nodeID == "" || token == "" {
		conn.WriteJSON(Message{
			Type: MessageTypeError,
			Data: map[string]interface{}{"error": "Missing node_id or token"},
		})
		conn.Close()
		return
	}

	// 验证节点Token
	node, err := m.nodeService.ValidateNodeToken(token)
	if err != nil || node.ID != nodeID {
		conn.WriteJSON(Message{
			Type: MessageTypeError,
			Data: map[string]interface{}{"error": "Invalid credentials"},
		})
		conn.Close()
		return
	}

	// 创建客户端
	client := &Client{
		ID:       nodeID,
		Type:     ClientTypeNode,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Manager:  m,
		LastPing: time.Now(),
		IsActive: true,
		NodeInfo: node,
	}

	// 注册客户端
	m.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// HandleAdminWebSocket 处理管理员WebSocket连接
func (m *WebSocketManager) HandleAdminWebSocket(c *gin.Context) {
	// 从上下文获取用户信息（由认证中间件设置）
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userModel, ok := user.(*models.User)
	if !ok {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user"})
		return
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}

	// 创建管理员客户端
	client := &Client{
		ID:       fmt.Sprintf("admin_%d", userModel.ID),
		Type:     ClientTypeAdmin,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Manager:  m,
		LastPing: time.Now(),
		IsActive: true,
		UserInfo: userModel.ToUserInfo(),
	}

	// 注册客户端
	m.register <- client

	// 启动读写协程
	go client.writePump()
	go client.readPump()
}

// SendToNode 发送消息到指定节点
func (m *WebSocketManager) SendToNode(nodeID string, message Message) error {
	m.mutex.RLock()
	client, exists := m.clients[nodeID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("node %s not connected", nodeID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	select {
	case client.Send <- data:
		return nil
	default:
		return fmt.Errorf("client send channel is full")
	}
}

// SendToAdmin 发送消息到指定管理员
func (m *WebSocketManager) SendToAdmin(userID string, message Message) error {
	m.mutex.RLock()
	client, exists := m.adminClients[userID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("admin %s not connected", userID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	select {
	case client.Send <- data:
		return nil
	default:
		return fmt.Errorf("client send channel is full")
	}
}

// BroadcastToAdmins 广播消息给所有管理员
func (m *WebSocketManager) BroadcastToAdmins(message Message) {
	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("Failed to marshal broadcast message: %v", err)
		return
	}

	select {
	case m.broadcast <- data:
	default:
		log.Printf("Broadcast channel is full")
	}
}

// GetConnectedNodes 获取已连接的节点列表
func (m *WebSocketManager) GetConnectedNodes() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	nodes := make([]string, 0, len(m.clients))
	for nodeID := range m.clients {
		nodes = append(nodes, nodeID)
	}

	return nodes
}

// GetConnectedAdmins 获取已连接的管理员列表
func (m *WebSocketManager) GetConnectedAdmins() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	admins := make([]string, 0, len(m.adminClients))
	for userID := range m.adminClients {
		admins = append(admins, userID)
	}

	return admins
}

// GetConnectionStats 获取连接统计信息
func (m *WebSocketManager) GetConnectionStats() map[string]interface{} {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	return map[string]interface{}{
		"connected_nodes":   len(m.clients),
		"connected_admins":  len(m.adminClients),
		"total_connections": len(m.clients) + len(m.adminClients),
	}
}

// readPump 读取消息泵
func (c *Client) readPump() {
	defer func() {
		c.Manager.unregister <- c
		c.Conn.Close()
	}()

	// 设置读取超时
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		c.LastPing = time.Now()
		return nil
	})

	for {
		var message Message
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		c.LastPing = time.Now()
		c.handleMessage(&message)
	}
}

// writePump 写入消息泵
func (c *Client) writePump() {
	ticker := time.NewTicker(54 * time.Second) // 54秒ping间隔
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket write error: %v", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage 处理接收到的消息
func (c *Client) handleMessage(message *Message) {
	switch message.Type {
	case MessageTypeHeartbeat:
		c.handleHeartbeat(message)

	case MessageTypeNodeStatus:
		c.handleNodeStatus(message)

	case MessageTypeNodeResponse:
		c.handleNodeResponse(message)

	case MessageTypeDeployStatus:
		c.handleDeployStatus(message)

	case MessageTypeAttackData:
		c.handleAttackData(message)

	case MessageTypeLogData:
		c.handleLogData(message)

	default:
		log.Printf("Unknown message type: %s from client %s", message.Type, c.ID)
	}
}

// handleHeartbeat 处理心跳消息
func (c *Client) handleHeartbeat(message *Message) {
	response := Message{
		Type:      MessageTypeHeartbeat,
		From:      "server",
		To:        c.ID,
		Data:      map[string]interface{}{"status": "ok"},
		Timestamp: time.Now(),
	}

	if data, err := json.Marshal(response); err == nil {
		select {
		case c.Send <- data:
		default:
		}
	}
}

// handleNodeStatus 处理节点状态消息
func (c *Client) handleNodeStatus(message *Message) {
	if c.Type != ClientTypeNode {
		return
	}

	// 更新节点心跳时间
	if c.Manager.nodeService != nil {
		c.Manager.nodeService.UpdateNodeHeartbeat(c.ID)
	}

	// 转发给管理员客户端
	c.Manager.BroadcastToAdmins(*message)
}

// handleNodeResponse 处理节点响应消息
func (c *Client) handleNodeResponse(message *Message) {
	if c.Type != ClientTypeNode {
		return
	}

	// 转发给管理员客户端
	c.Manager.BroadcastToAdmins(*message)
}

// handleDeployStatus 处理部署状态消息
func (c *Client) handleDeployStatus(message *Message) {
	if c.Type != ClientTypeNode {
		return
	}

	// 解析部署状态数据
	statusData, ok := message.Data.(map[string]interface{})
	if !ok {
		log.Printf("Invalid deploy status data from node %s", c.ID)
		return
	}

	// 提取部署信息
	deploymentID, _ := statusData["deployment_id"].(string)
	status, _ := statusData["status"].(string)
	containerID, _ := statusData["container_id"].(string)
	errorMsg, _ := statusData["error"].(string)

	if deploymentID == "" || status == "" {
		log.Printf("Missing deployment_id or status in deploy status from node %s", c.ID)
		return
	}

	// 更新数据库中的部署状态
	if c.Manager.deploymentService != nil {
		// 根据部署ID查找部署记录
		if deployment, err := c.Manager.deploymentService.GetDeploymentByContainerID(containerID); err == nil {
			// 更新部署状态
			if err := c.Manager.deploymentService.UpdateDeploymentStatus(deployment.ID, status, containerID); err != nil {
				log.Printf("Failed to update deployment status: %v", err)
			} else {
				log.Printf("Updated deployment %d status to %s", deployment.ID, status)
				if errorMsg != "" {
					log.Printf("Deployment error: %s", errorMsg)
				}
			}
		} else {
			log.Printf("Deployment not found for container %s: %v", containerID, err)
		}
	}

	// 转发给管理员客户端进行实时更新
	c.Manager.BroadcastToAdmins(*message)
}

// handleAttackData 处理攻击数据消息
func (c *Client) handleAttackData(message *Message) {
	if c.Type != ClientTypeNode {
		return
	}

	// 这里可以处理实时攻击数据
	// 转发给管理员进行实时监控
	c.Manager.BroadcastToAdmins(*message)
}

// handleLogData 处理日志数据消息
func (c *Client) handleLogData(message *Message) {
	if c.Type != ClientTypeNode {
		return
	}

	// 处理节点日志数据
	// 可以存储到数据库或转发给管理员
	c.Manager.BroadcastToAdmins(*message)
}
