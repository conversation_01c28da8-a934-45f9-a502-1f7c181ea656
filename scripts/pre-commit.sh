#!/bin/bash

# Honeypot Admin Panel 提交前检查脚本
# 用于确保代码质量和项目完整性

set -e

echo "🔍 开始提交前检查..."

# 检查Go版本
echo "📋 检查Go版本..."
go version

# 格式化代码
echo "🎨 格式化Go代码..."
go fmt ./...

# 检查代码质量
echo "🔍 运行go vet检查..."
go vet ./...

# 检查模块依赖
echo "📦 检查模块依赖..."
go mod tidy
go mod verify

# 编译检查
echo "🔨 编译检查..."
go build -o /tmp/honeypot-admin ./cmd/admin
rm -f /tmp/honeypot-admin

# 运行测试（如果有）
if [ -f "*_test.go" ]; then
    echo "🧪 运行测试..."
    go test ./...
fi

# 检查敏感文件
echo "🔒 检查敏感文件..."
SENSITIVE_FILES=(
    "configs/config.yaml"
    "*.key"
    "*.pem"
    "*.p12"
    "*.secret"
    ".env"
)

for pattern in "${SENSITIVE_FILES[@]}"; do
    if ls $pattern 1> /dev/null 2>&1; then
        echo "⚠️  警告: 发现敏感文件 $pattern，请确保已添加到.gitignore"
    fi
done

# 检查大文件
echo "📏 检查大文件..."
find . -type f -size +10M -not -path "./.git/*" -not -path "./data/*" -not -path "./bin/*" | while read file; do
    echo "⚠️  警告: 发现大文件 $file (>10MB)"
done

# 检查必要文件
echo "📄 检查必要文件..."
REQUIRED_FILES=(
    "README.md"
    ".gitignore"
    "go.mod"
    "configs/config.example.yaml"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 错误: 缺少必要文件 $file"
        exit 1
    fi
done

# 检查目录结构
echo "📁 检查目录结构..."
REQUIRED_DIRS=(
    "cmd/admin"
    "internal/handlers"
    "internal/models"
    "internal/services"
    "configs"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "❌ 错误: 缺少必要目录 $dir"
        exit 1
    fi
done

# 检查配置文件
echo "⚙️  检查配置文件..."
if [ -f "configs/config.yaml" ]; then
    echo "⚠️  警告: 发现配置文件 configs/config.yaml，请确保不包含敏感信息"
fi

# 检查数据库文件
echo "🗄️  检查数据库文件..."
if [ -d "data" ] && [ "$(ls -A data)" ]; then
    echo "ℹ️  信息: data目录包含文件，这些文件不会被提交（已在.gitignore中排除）"
fi

# 检查编译产物
echo "🔧 检查编译产物..."
if [ -d "bin" ] && [ "$(ls -A bin)" ]; then
    echo "ℹ️  信息: bin目录包含编译产物，这些文件不会被提交（已在.gitignore中排除）"
fi

echo "✅ 提交前检查完成！"
echo ""
echo "📝 提交建议："
echo "1. 确保所有敏感信息已从代码中移除"
echo "2. 检查提交信息是否清晰描述了变更内容"
echo "3. 确认所有新增的配置项都有对应的示例"
echo "4. 如果修改了API接口，请更新相关文档"
echo ""
echo "🚀 可以安全提交了！"
