<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind CSS 测试页面</title>
    <link href="web/static/css/tailwind.css" rel="stylesheet">
</head>
<body>
    <div class="min-h-screen bg-gray-50 py-12">
        <div class="max-w-4xl mx-auto px-4">
            <h1 class="text-4xl font-bold text-center text-gray-900 mb-8">
                🎉 Tailwind CSS 配置成功！
            </h1>
            
            <!-- 测试卡片组件 -->
            <div class="card-modern p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">现代化卡片组件</h2>
                <p class="text-gray-600 mb-4">这是一个使用自定义组件样式的现代化卡片。</p>
                <div class="flex gap-4">
                    <button class="btn-primary">主要按钮</button>
                    <button class="btn-secondary">次要按钮</button>
                </div>
            </div>
            
            <!-- 测试表单组件 -->
            <div class="card-modern p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">表单组件</h2>
                <form class="space-y-4">
                    <div>
                        <label class="form-label">用户名</label>
                        <input type="text" class="form-input" placeholder="请输入用户名">
                    </div>
                    <div>
                        <label class="form-label">密码</label>
                        <input type="password" class="form-input" placeholder="请输入密码">
                    </div>
                    <button type="submit" class="btn-primary w-full">登录</button>
                </form>
            </div>
            
            <!-- 测试状态徽章 -->
            <div class="card-modern p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">状态徽章</h2>
                <div class="flex gap-2 flex-wrap">
                    <span class="badge-success">运行中</span>
                    <span class="badge-warning">警告</span>
                    <span class="badge-danger">错误</span>
                    <span class="badge-info">信息</span>
                </div>
            </div>
            
            <!-- 测试响应式网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="card-modern p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">节点管理</h3>
                    <p class="text-gray-600 text-sm">管理蜜罐节点状态</p>
                </div>
                <div class="card-modern p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">模板配置</h3>
                    <p class="text-gray-600 text-sm">配置蜜罐模板</p>
                </div>
                <div class="card-modern p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">实时监控</h3>
                    <p class="text-gray-600 text-sm">监控系统状态</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
