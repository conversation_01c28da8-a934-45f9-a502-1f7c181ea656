/* 仪表板样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* 边框颜色 */
.border-left-primary {
    border-left: 0.25rem solid #667eea !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

/* 文本颜色 */
.text-primary {
    color: #667eea !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-400 {
    color: #d1d3e2 !important;
}

.text-gray-500 {
    color: #b7b9cc !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

/* 字体样式 */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* 表格样式 */
.table {
    color: #858796;
}

.table th {
    background-color: #f8f9fc;
    border-color: #e3e6f0;
    font-weight: 600;
    font-size: 0.85rem;
    color: #5a5c69;
}

.table td {
    border-color: #e3e6f0;
    font-size: 0.85rem;
}

.table-responsive {
    border-radius: 0.35rem;
}

/* 按钮样式 */
.btn {
    border-radius: 0.35rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
    color: #fff;
}

/* 状态徽章 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 10rem;
}

.badge-success {
    background-color: #1cc88a;
}

.badge-warning {
    background-color: #f6c23e;
}

.badge-danger {
    background-color: #e74a3b;
}

.badge-secondary {
    background-color: #858796;
}

/* 下拉菜单 */
.dropdown-menu {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 0.35rem;
}

.dropdown-item {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .h5 {
        font-size: 1.1rem;
    }
    
    .text-xs {
        font-size: 0.65rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 图标样式 */
.fa-2x {
    font-size: 2em;
}

.bi {
    vertical-align: -0.125em;
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 卡片头部 */
.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 0.35rem 0.35rem 0 0 !important;
}

/* 无边距行 */
.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}

/* 工具提示 */
.tooltip {
    font-size: 0.75rem;
}

/* 进度条 */
.progress {
    height: 0.5rem;
    border-radius: 10rem;
}

.progress-bar {
    border-radius: 10rem;
}

/* 列表组 */
.list-group-item {
    border: 1px solid #e3e6f0;
    font-size: 0.85rem;
}

.list-group-item:first-child {
    border-top-left-radius: 0.35rem;
    border-top-right-radius: 0.35rem;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0.35rem;
    border-bottom-right-radius: 0.35rem;
}

/* 输入组 */
.input-group-text {
    background-color: #f8f9fc;
    border-color: #d1d3e2;
    color: #6e707e;
}

.form-control {
    border-color: #d1d3e2;
    font-size: 0.85rem;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 攻击数据分析样式 */
.stat-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stat-card h6 {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.stat-card h4 {
    margin: 0;
    font-weight: 700;
}

.flag-icon {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: 2px;
}

.threat-level-critical {
    color: #dc3545;
}

.threat-level-high {
    color: #fd7e14;
}

.threat-level-medium {
    color: #ffc107;
}

.threat-level-low {
    color: #198754;
}

.analysis-tab-content {
    min-height: 400px;
}

/* 图表容器 */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* 分析卡片动画 */
.analysis-card {
    transition: transform 0.2s ease-in-out;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 威胁级别指示器 */
.threat-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.threat-indicator.critical {
    background-color: #dc3545;
}

.threat-indicator.high {
    background-color: #fd7e14;
}

.threat-indicator.medium {
    background-color: #ffc107;
}

.threat-indicator.low {
    background-color: #198754;
}

/* 数据加载状态 */
.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
    color: #6c757d;
}

/* 标签页样式优化 */
.nav-tabs .nav-link {
    border: none;
    border-bottom: 2px solid transparent;
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom-color: #667eea;
    color: #667eea;
}

.nav-tabs .nav-link:hover {
    border-bottom-color: #667eea;
    color: #667eea;
}

/* 时间范围选择器 */
.time-range-selector {
    min-width: 120px;
}

/* 攻击类型徽章 */
.attack-type-badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    margin: 0.1rem;
}

/* 地理分布表格 */
.geographic-table .progress {
    height: 20px;
}

.geographic-table .progress-bar {
    font-size: 0.75rem;
    line-height: 20px;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }

    .stat-card {
        margin-bottom: 1rem;
    }

    .nav-tabs .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
}
