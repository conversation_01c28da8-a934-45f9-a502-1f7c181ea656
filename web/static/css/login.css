/* 登录页面样式 */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.card {
    border-radius: 15px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-body {
    padding: 2.5rem !important;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    border: 2px solid #e9ecef;
    border-right: none;
    background-color: #f8f9fa;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-secondary {
    border-radius: 0 10px 10px 0;
    border: 2px solid #e9ecef;
    border-left: none;
}

.alert {
    border-radius: 10px;
    border: none;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeInUp 0.6s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 2rem !important;
    }
    
    .col-md-6:first-child {
        display: none !important;
    }
}

/* 加载动画 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 图标样式 */
.bi {
    color: #667eea;
}

.text-primary {
    color: #667eea !important;
}

/* 链接样式 */
a {
    color: #667eea;
    transition: color 0.3s ease;
}

a:hover {
    color: #764ba2;
}

/* 输入框聚焦效果 */
.input-group:focus-within .input-group-text {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

/* 背景装饰 */
.bg-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* 卡片阴影效果 */
.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* 按钮加载状态 */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 表单验证样式 */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #28a745;
}

/* 密码显示/隐藏按钮 */
#toggle-password {
    cursor: pointer;
}

#toggle-password:hover {
    background-color: #e9ecef;
}

/* 记住我复选框 */
.form-check-input {
    border-radius: 4px;
}

/* 系统信息 */
.text-muted {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* 错误提示动画 */
.alert {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
