/* CSS变量定义 - 统一主题色彩 */
:root {
  --primary-color: #667eea;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-color: #1cc88a;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --info-color: #36b9cc;
  --dark-color: #5a5c69;
  --light-bg: #f8f9fc;
  --border-color: #e3e6f0;
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --border-radius: 0.5rem;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局样式优化 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  background-color: var(--light-bg);
  line-height: 1.6;
  color: var(--dark-color);
  font-size: 14px;
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.15s ease-in-out;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
    font-weight: 600;
}

.sidebar .nav-link i {
    margin-right: 0.5rem;
    width: 16px;
    text-align: center;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* 主内容区域 */
main {
    margin-left: 240px;
    padding-top: 56px;
}

@media (max-width: 767.98px) {
    main {
        margin-left: 0;
    }
    
    .sidebar {
        position: static;
        height: auto;
    }
}

/* 现代化卡片设计 */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* 边框颜色样式 */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

/* 文本颜色 */
.text-primary {
    color: #4e73df !important;
}

.text-success {
    color: #1cc88a !important;
}

.text-info {
    color: #36b9cc !important;
}

.text-warning {
    color: #f6c23e !important;
}

.text-danger {
    color: #e74a3b !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* 字体样式 */
.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

/* 图表容器 */
.chart-area {
    position: relative;
    height: 10rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

/* 现代化表格设计 */
.table {
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  background: white;
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fc 0%, #e9ecef 100%);
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
}

.table tbody tr {
  transition: var(--transition);
}

.table tbody tr:hover {
  background-color: rgba(102, 126, 234, 0.05);
  transform: scale(1.01);
}

/* 徽章样式 */
.badge {
    font-size: 0.75em;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
}

/* 现代化按钮系统 */
.btn {
  border-radius: var(--border-radius);
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  transition: var(--transition);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-success {
    background-color: #1cc88a;
    border-color: #1cc88a;
}

.btn-success:hover {
    background-color: #17a673;
    border-color: #169b6b;
}

.btn-info {
    background-color: #36b9cc;
    border-color: #36b9cc;
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
}

.btn-warning {
    background-color: #f6c23e;
    border-color: #f6c23e;
}

.btn-warning:hover {
    background-color: #f4b619;
    border-color: #f4b30d;
}

.btn-danger {
    background-color: #e74a3b;
    border-color: #e74a3b;
}

.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #d52a1a;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.5rem 1rem;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #858796;
}

/* 页面标题 */
.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    color: #5a5c69;
    font-weight: 400;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online {
    background-color: #1cc88a;
}

.status-offline {
    background-color: #e74a3b;
}

.status-warning {
    background-color: #f6c23e;
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 2rem;
    color: #858796;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #858796;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #dddfeb;
}

.empty-state h4 {
    color: #5a5c69;
    margin-bottom: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .h5 {
        font-size: 1rem;
    }
    
    .chart-area {
        height: 8rem;
    }
    
    .chart-pie {
        height: 12rem;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 0.5rem;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 0.25rem;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.modal-footer {
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

/* 表单样式 */
.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: #4e73df;
    border-color: #dddfeb;
}

.pagination .page-link:hover {
    color: #2e59d9;
    background-color: #eaecf4;
    border-color: #dddfeb;
}

.pagination .page-item.active .page-link {
    background-color: #4e73df;
    border-color: #4e73df;
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    background-color: #eaecf4;
}

.progress-bar {
    background-color: #4e73df;
}
