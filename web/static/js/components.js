/* 蜜罐管理平台 - 构建时间: 2025-07-30T07:08:16.394Z */

/* === main.js === */
// 全局JavaScript功能

// API基础配置
const API_BASE_URL = '/api/v1';

// 全局变量
let currentUser = null;
let authToken = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 检查认证状态
    checkAuthStatus();
    
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化模态框
    initializeModals();
    
    // 绑定全局事件
    bindGlobalEvents();
}

// 检查认证状态
function checkAuthStatus() {
    authToken = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');
    
    if (authToken && userStr) {
        try {
            currentUser = JSON.parse(userStr);
            // 验证Token是否有效
            validateToken();
        } catch (e) {
            console.error('Failed to parse user data:', e);
            logout();
        }
    } else {
        // 如果不在登录页面，重定向到登录页面
        if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
        }
    }
}

// 验证Token
function validateToken() {
    fetch(`${API_BASE_URL}/auth/profile`, {
        headers: {
            'Authorization': `Bearer ${authToken}`
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Token validation failed');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            currentUser = data.data;
            localStorage.setItem('user', JSON.stringify(currentUser));
        } else {
            throw new Error('Invalid token');
        }
    })
    .catch(error => {
        console.error('Token validation error:', error);
        logout();
    });
}

// 登出
function logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    authToken = null;
    currentUser = null;
    window.location.href = '/login';
}

// 初始化工具提示
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化模态框
function initializeModals() {
    // 为所有模态框添加事件监听器
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('hidden.bs.modal', function () {
            // 清理模态框内容
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
            }
        });
    });
}

// 绑定全局事件
function bindGlobalEvents() {
    // 绑定登出按钮
    const logoutLinks = document.querySelectorAll('a[href="/logout"]');
    logoutLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    });
    
    // 绑定确认删除按钮
    const deleteButtons = document.querySelectorAll('[data-action="delete"]');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const message = this.getAttribute('data-message') || '确定要删除这个项目吗？';
            if (confirm(message)) {
                const url = this.getAttribute('href') || this.getAttribute('data-url');
                if (url) {
                    deleteResource(url);
                }
            }
        });
    });
}

// API请求封装
class ApiClient {
    constructor(baseURL = API_BASE_URL) {
        this.baseURL = baseURL;
    }
    
    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (authToken) {
            headers['Authorization'] = `Bearer ${authToken}`;
        }
        
        return headers;
    }
    
    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(`${this.baseURL}${endpoint}`, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });
        
        const response = await fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }
    
    // POST请求
    async post(endpoint, data = {}) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });
        
        return this.handleResponse(response);
    }
    
    // PUT请求
    async put(endpoint, data = {}) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method: 'PUT',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });
        
        return this.handleResponse(response);
    }
    
    // DELETE请求
    async delete(endpoint) {
        const response = await fetch(`${this.baseURL}${endpoint}`, {
            method: 'DELETE',
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }
    
    // 处理响应
    async handleResponse(response) {
        if (response.status === 401) {
            logout();
            throw new Error('Unauthorized');
        }
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Request failed');
        }
        
        return data;
    }
}

// 创建API客户端实例
const api = new ApiClient();

// 通用功能函数

// 显示提示消息
function showAlert(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)}"></i> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 移除现有的提示
    const existingAlerts = document.querySelectorAll('#flash-messages .alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // 添加新提示
    const flashContainer = document.getElementById('flash-messages');
    if (flashContainer) {
        flashContainer.appendChild(alertDiv);
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                alertDiv.remove();
            }, duration);
        }
    }
}

// 获取提示图标
function getAlertIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'danger': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}

// 显示加载状态
function showLoading(element, text = '加载中...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
    }
}

// 隐藏加载状态
function hideLoading(element, content = '') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = content;
    }
}

// 格式化时间
function formatTime(timeStr, format = 'datetime') {
    if (!timeStr) return '从未';
    
    const date = new Date(timeStr);
    
    switch (format) {
        case 'date':
            return date.toLocaleDateString('zh-CN');
        case 'time':
            return date.toLocaleTimeString('zh-CN');
        case 'datetime':
            return date.toLocaleString('zh-CN');
        case 'relative':
            return getRelativeTime(date);
        default:
            return date.toLocaleString('zh-CN');
    }
}

// 获取相对时间
function getRelativeTime(date) {
    const now = new Date();
    const diff = now - date;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
        return `${days}天前`;
    } else if (hours > 0) {
        return `${hours}小时前`;
    } else if (minutes > 0) {
        return `${minutes}分钟前`;
    } else {
        return '刚刚';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    } else {
        return num.toString();
    }
}

// 删除资源
async function deleteResource(url) {
    try {
        showLoading('#main-content', '删除中...');
        
        const response = await fetch(url, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('删除成功', 'success');
            // 刷新页面或移除元素
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert(data.message || '删除失败', 'danger');
        }
    } catch (error) {
        console.error('Delete error:', error);
        showAlert('删除失败: ' + error.message, 'danger');
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showAlert('已复制到剪贴板', 'success', 2000);
        }).catch(err => {
            console.error('Failed to copy:', err);
            showAlert('复制失败', 'danger');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showAlert('已复制到剪贴板', 'success', 2000);
        } catch (err) {
            console.error('Failed to copy:', err);
            showAlert('复制失败', 'danger');
        }
        document.body.removeChild(textArea);
    }
}

// 导出数据
function exportData(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 分页处理
function handlePagination(currentPage, totalPages, baseUrl) {
    const pagination = document.querySelector('.pagination');
    if (!pagination) return;
    
    pagination.innerHTML = '';
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="${baseUrl}?page=${currentPage - 1}">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="${baseUrl}?page=${i}">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage >= totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="${baseUrl}?page=${currentPage + 1}">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 表格排序
function sortTable(table, column, direction = 'asc') {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aVal = a.cells[column].textContent.trim();
        const bVal = b.cells[column].textContent.trim();
        
        if (direction === 'asc') {
            return aVal.localeCompare(bVal);
        } else {
            return bVal.localeCompare(aVal);
        }
    });
    
    rows.forEach(row => tbody.appendChild(row));
}

// 表格搜索
function searchTable(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const match = text.includes(searchTerm.toLowerCase());
        row.style.display = match ? '' : 'none';
    });
}


/* === dashboard.js === */
// 仪表板页面JavaScript

// 全局变量
let overviewChart = null;
let nodeStatusChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuth();
    
    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
    
    // 定时刷新数据
    setInterval(refreshDashboard, 30000); // 每30秒刷新一次
});

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 设置用户信息
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    if (userInfo.username) {
        document.getElementById('user-name').textContent = userInfo.username;
    }
}

// 初始化页面
function initializePage() {
    // 加载仪表板数据
    loadDashboardData();
    
    // 初始化图表
    initializeCharts();
    
    // 检查服务状态
    checkServiceStatus();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// 加载仪表板数据
async function loadDashboardData() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        // 更新统计卡片
        updateStatisticsCards(data);
        
        // 更新最近活动
        updateRecentActivities(data.recent_activities || []);
        
    } catch (error) {
        console.error('Load dashboard data failed:', error);
        showError('加载仪表板数据失败: ' + error.message);
    }
}

// 更新统计卡片
function updateStatisticsCards(data) {
    document.getElementById('online-nodes').textContent = data.node_stats?.online || '0';
    document.getElementById('active-deployments').textContent = data.deployment_stats?.running || '0';
    document.getElementById('today-attacks').textContent = data.attack_stats?.today || '0';
    document.getElementById('total-templates').textContent = data.template_stats?.total || '0';
}

// 初始化图表
function initializeCharts() {
    // 系统概览图表
    const overviewCtx = document.getElementById('overview-chart').getContext('2d');
    overviewChart = new Chart(overviewCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '攻击次数',
                data: [],
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }, {
                label: '节点活动',
                data: [],
                borderColor: '#4BC0C0',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // 节点状态分布饼图
    const nodeStatusCtx = document.getElementById('node-status-chart').getContext('2d');
    nodeStatusChart = new Chart(nodeStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['在线', '离线', '错误'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: [
                    '#28a745',
                    '#dc3545',
                    '#ffc107'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // 加载图表数据
    loadChartData();
}

// 加载图表数据
async function loadChartData() {
    try {
        // 加载节点状态数据
        const nodesResponse = await fetch('/api/v1/nodes', {
            headers: getAuthHeaders()
        });

        if (nodesResponse.ok) {
            const nodesData = await nodesResponse.json();
            if (nodesData.success) {
                updateNodeStatusChart(nodesData.data);
            }
        }

        // 加载攻击趋势数据（如果有情报数据API）
        try {
            const attacksResponse = await fetch('/api/v1/intelligence/statistics', {
                headers: getAuthHeaders()
            });

            if (attacksResponse.ok) {
                const attacksData = await attacksResponse.json();
                if (attacksData.success) {
                    updateOverviewChart(attacksData.data);
                }
            }
        } catch (error) {
            console.log('Intelligence data not available:', error);
            // 使用默认数据
            updateOverviewChart(null);
        }

    } catch (error) {
        console.error('Load chart data failed:', error);
    }
}

// 更新节点状态图表
function updateNodeStatusChart(nodes) {
    if (!nodeStatusChart || !nodes) return;

    const statusCounts = { online: 0, offline: 0, error: 0 };

    nodes.forEach(node => {
        if (node.status === 'online') {
            statusCounts.online++;
        } else if (node.status === 'offline') {
            statusCounts.offline++;
        } else {
            statusCounts.error++;
        }
    });

    nodeStatusChart.data.datasets[0].data = [
        statusCounts.online,
        statusCounts.offline,
        statusCounts.error
    ];
    nodeStatusChart.update();
}

// 更新系统概览图表
function updateOverviewChart(data) {
    if (!overviewChart) return;

    if (data && data.hourly_stats) {
        // 使用真实数据
        const labels = data.hourly_stats.map(stat => `${stat.hour}:00`);
        const attackData = data.hourly_stats.map(stat => stat.count);

        overviewChart.data.labels = labels;
        overviewChart.data.datasets[0].data = attackData;
        overviewChart.data.datasets[1].data = new Array(labels.length).fill(0); // 节点活动数据暂时为空
    } else {
        // 使用默认数据
        const hours = Array.from({length: 24}, (_, i) => `${i}:00`);
        overviewChart.data.labels = hours;
        overviewChart.data.datasets[0].data = new Array(24).fill(0);
        overviewChart.data.datasets[1].data = new Array(24).fill(0);
    }

    overviewChart.update();
}

// 更新最近活动
function updateRecentActivities(activities) {
    const tbody = document.getElementById('recent-activities');

    if (!activities || activities.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无最近活动</td></tr>';
        return;
    }

    tbody.innerHTML = activities.map(activity => `
        <tr>
            <td>${formatTime(activity.created_at || activity.time)}</td>
            <td>${activity.description || activity.event}</td>
            <td><span class="badge bg-${getStatusColor(activity.status)}">${getStatusText(activity.status)}</span></td>
        </tr>
    `).join('');
}

// 检查服务状态
async function checkServiceStatus() {
    // 检查DecoyWatch状态
    try {
        const response = await fetch('/api/v1/intelligence/health', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            document.getElementById('decoywatch-status').className = 'badge bg-success';
            document.getElementById('decoywatch-status').textContent = '正常';
        } else {
            throw new Error('Service unavailable');
        }
    } catch (error) {
        document.getElementById('decoywatch-status').className = 'badge bg-danger';
        document.getElementById('decoywatch-status').textContent = '异常';
    }
    
    // 检查WebSocket状态（模拟）
    document.getElementById('websocket-status').className = 'badge bg-success';
    document.getElementById('websocket-status').textContent = '已连接';
    
    // 更新系统资源使用率（模拟）
    updateSystemResources();
}

// 更新系统资源使用率
function updateSystemResources() {
    // 模拟CPU使用率
    const cpuUsage = Math.floor(Math.random() * 30) + 20; // 20-50%
    const cpuBar = document.getElementById('cpu-usage');
    cpuBar.style.width = cpuUsage + '%';
    cpuBar.textContent = cpuUsage + '%';
    
    // 模拟内存使用率
    const memoryUsage = Math.floor(Math.random() * 40) + 30; // 30-70%
    const memoryBar = document.getElementById('memory-usage');
    memoryBar.style.width = memoryUsage + '%';
    memoryBar.textContent = memoryUsage + '%';
}

// 刷新仪表板
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    Promise.all([
        loadDashboardData(),
        checkServiceStatus()
    ]).finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 获取状态颜色
function getStatusColor(status) {
    switch (status) {
        case 'success': return 'success';
        case 'info': return 'info';
        case 'warning': return 'warning';
        case 'error': return 'danger';
        default: return 'secondary';
    }
}

// 获取状态文本
function getStatusText(status) {
    switch (status) {
        case 'success': return '成功';
        case 'info': return '信息';
        case 'warning': return '警告';
        case 'error': return '错误';
        default: return '未知';
    }
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';

    try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (error) {
        return timeStr;
    }
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data.data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}


/* === login.js === */
// 登录页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    const loginSpinner = document.getElementById('login-spinner');
    const errorAlert = document.getElementById('error-alert');
    const errorMessage = document.getElementById('error-message');
    const togglePassword = document.getElementById('toggle-password');
    const passwordInput = document.getElementById('password');
    const usernameInput = document.getElementById('username');

    // 密码显示/隐藏切换
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = togglePassword.querySelector('i');
            if (type === 'password') {
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            } else {
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            }
        });
    }

    // 表单验证
    function validateForm() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!username) {
            showError('请输入用户名');
            usernameInput.focus();
            return false;
        }
        
        if (username.length < 3) {
            showError('用户名至少需要3个字符');
            usernameInput.focus();
            return false;
        }
        
        if (!password) {
            showError('请输入密码');
            passwordInput.focus();
            return false;
        }
        
        if (password.length < 6) {
            showError('密码至少需要6个字符');
            passwordInput.focus();
            return false;
        }
        
        return true;
    }

    // 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            hideError();
        }, 3000);
    }

    // 隐藏错误信息
    function hideError() {
        errorAlert.classList.add('d-none');
    }

    // 设置加载状态
    function setLoading(loading) {
        if (loading) {
            loginBtn.disabled = true;
            loginSpinner.classList.remove('d-none');
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>登录中...';
        } else {
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
            loginBtn.innerHTML = '登录';
        }
    }

    // 登录请求
    async function login(username, password) {
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 登录成功
                const token = data.data.token;
                const userInfo = data.data.user_info;
                
                // 保存token到localStorage
                localStorage.setItem('token', token);
                localStorage.setItem('user_info', JSON.stringify(userInfo));
                
                // 检查是否记住我
                const rememberMe = document.getElementById('remember-me').checked;
                if (rememberMe) {
                    localStorage.setItem('remember_me', 'true');
                    localStorage.setItem('username', username);
                } else {
                    localStorage.removeItem('remember_me');
                    localStorage.removeItem('username');
                }
                
                // 显示成功消息
                showSuccess('登录成功，正在跳转...');
                
                // 延迟跳转到仪表板
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
                
            } else {
                // 登录失败
                const errorMsg = data.message || '登录失败，请检查用户名和密码';
                showError(errorMsg);
            }
        } catch (error) {
            console.error('Login error:', error);
            showError('网络错误，请稍后重试');
        }
    }

    // 显示成功信息
    function showSuccess(message) {
        // 隐藏错误信息
        hideError();
        
        // 创建成功提示
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success';
        successAlert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            ${message}
        `;
        
        // 插入到表单前面
        loginForm.parentNode.insertBefore(successAlert, loginForm);
        
        // 3秒后移除
        setTimeout(() => {
            successAlert.remove();
        }, 3000);
    }

    // 表单提交事件
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 隐藏之前的错误信息
            hideError();
            
            // 验证表单
            if (!validateForm()) {
                return;
            }
            
            // 设置加载状态
            setLoading(true);
            
            // 获取表单数据
            const username = usernameInput.value.trim();
            const password = passwordInput.value.trim();
            
            // 执行登录
            await login(username, password);
            
            // 取消加载状态
            setLoading(false);
        });
    }

    // 输入框事件监听
    [usernameInput, passwordInput].forEach(input => {
        if (input) {
            input.addEventListener('input', function() {
                // 输入时隐藏错误信息
                hideError();
                
                // 移除验证样式
                input.classList.remove('is-invalid', 'is-valid');
            });
            
            input.addEventListener('blur', function() {
                // 失焦时验证
                const value = input.value.trim();
                if (value) {
                    if (input === usernameInput && value.length >= 3) {
                        input.classList.add('is-valid');
                        input.classList.remove('is-invalid');
                    } else if (input === passwordInput && value.length >= 6) {
                        input.classList.add('is-valid');
                        input.classList.remove('is-invalid');
                    } else {
                        input.classList.add('is-invalid');
                        input.classList.remove('is-valid');
                    }
                }
            });
        }
    });

    // 页面加载时检查是否记住用户名
    const rememberedUsername = localStorage.getItem('username');
    const rememberMe = localStorage.getItem('remember_me');
    
    if (rememberMe === 'true' && rememberedUsername) {
        usernameInput.value = rememberedUsername;
        document.getElementById('remember-me').checked = true;
        passwordInput.focus();
    } else {
        usernameInput.focus();
    }

    // 检查是否已经登录
    const token = localStorage.getItem('token');
    if (token) {
        // 验证token是否有效
        fetch('/api/v1/auth/profile', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (response.ok) {
                // token有效，直接跳转到仪表板
                window.location.href = '/dashboard';
            }
        })
        .catch(error => {
            // token无效，清除本地存储
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(e) {
        // ESC键清除错误信息
        if (e.key === 'Escape') {
            hideError();
        }
    });
});


/* === nodes.js === */
// 节点管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载节点数据
    loadNodesData();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadNodesData, 30000); // 30秒刷新一次
});

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let selectedNodes = new Set();

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载节点数据
async function loadNodesData() {
    try {
        // 加载统计数据
        await loadNodeStatistics();
        
        // 加载节点列表
        await loadNodesList();
        
    } catch (error) {
        console.error('Load nodes data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载节点统计
async function loadNodeStatistics() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const overview = data.data;
        
        // 更新统计数据
        document.getElementById('total-nodes').textContent = overview.nodes.total || 0;
        document.getElementById('online-nodes').textContent = overview.nodes.online || 0;
        document.getElementById('offline-nodes').textContent = overview.nodes.offline || 0;
        document.getElementById('running-services').textContent = overview.deployments.running || 0;
        
    } catch (error) {
        console.error('Load node statistics failed:', error);
        // 显示默认值
        document.getElementById('total-nodes').textContent = '0';
        document.getElementById('online-nodes').textContent = '0';
        document.getElementById('offline-nodes').textContent = '0';
        document.getElementById('running-services').textContent = '0';
    }
}

// 加载节点列表
async function loadNodesList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const statusFilter = document.getElementById('status-filter').value;
        const regionFilter = document.getElementById('region-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (statusFilter) params.append('status', statusFilter);
        if (regionFilter) params.append('region', regionFilter);
        
        const response = await fetch(`/api/v1/nodes?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const nodes = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新节点表格
        updateNodesTable(nodes);
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load nodes list failed:', error);
        updateNodesTable([]);
    }
}

// 更新节点表格
function updateNodesTable(nodes) {
    const tbody = document.querySelector('#nodes-table tbody');
    
    if (nodes.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无节点数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = nodes.map(node => {
        const statusBadge = getStatusBadge(node.status);
        const lastHeartbeat = formatTime(node.last_heartbeat_at);
        const runningServices = node.running_services || 0;
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="node-checkbox" value="${node.id}" 
                           onchange="toggleNodeSelection('${node.id}')">
                </td>
                <td><code>${node.id}</code></td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-hdd-network me-2 text-primary"></i>
                        ${node.name}
                    </div>
                </td>
                <td>${node.ip}</td>
                <td>
                    <span class="badge bg-secondary">${getRegionName(node.region)}</span>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <span class="badge bg-info">${runningServices} 个</span>
                </td>
                <td>${lastHeartbeat}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewNode('${node.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="controlNode('${node.id}')" 
                                title="控制节点">
                            <i class="bi bi-gear"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteNode('${node.id}')" 
                                title="删除节点">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'online': '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>在线</span>',
        'offline': '<span class="badge bg-secondary"><i class="bi bi-x-circle me-1"></i>离线</span>',
        'error': '<span class="badge bg-danger"><i class="bi bi-exclamation-triangle me-1"></i>错误</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取区域名称
function getRegionName(region) {
    const regions = {
        'asia': '亚洲',
        'europe': '欧洲',
        'america': '美洲'
    };
    return regions[region] || region;
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '从未';
    
    try {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now - time;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return time.toLocaleDateString();
        }
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }
    
    currentPage = page;
    loadNodesList();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadNodesList();
            }, 500);
        });
    }
    
    // 状态过滤
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1;
            loadNodesList();
        });
    }
    
    // 区域过滤
    const regionFilter = document.getElementById('region-filter');
    if (regionFilter) {
        regionFilter.addEventListener('change', function() {
            currentPage = 1;
            loadNodesList();
        });
    }
    
    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换节点选择
function toggleNodeSelection(nodeId) {
    if (selectedNodes.has(nodeId)) {
        selectedNodes.delete(nodeId);
    } else {
        selectedNodes.add(nodeId);
    }
    
    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.node-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedNodes.add(checkbox.value);
        } else {
            selectedNodes.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.node-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');
    
    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }
    
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    
    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新节点
function refreshNodes() {
    const refreshBtn = document.querySelector('button[onclick="refreshNodes()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    loadNodesData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 添加节点
async function addNode() {
    const form = document.getElementById('add-node-form');
    const formData = new FormData(form);
    
    const nodeData = {
        name: document.getElementById('node-name').value,
        ip: document.getElementById('node-ip').value,
        region: document.getElementById('node-region').value,
        description: document.getElementById('node-description').value
    };
    
    // 验证表单
    if (!nodeData.name || !nodeData.ip || !nodeData.region) {
        showError('请填写所有必填字段');
        return;
    }
    
    try {
        const response = await fetch('/api/v1/nodes', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(nodeData)
        });
        
        const data = await handleApiResponse(response);
        
        showSuccess('节点添加成功');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addNodeModal'));
        modal.hide();
        
        // 重置表单
        form.reset();
        
        // 刷新列表
        loadNodesData();
        
    } catch (error) {
        console.error('Add node failed:', error);
        showError('添加节点失败: ' + error.message);
    }
}

// 查看节点详情
function viewNode(nodeId) {
    // TODO: 实现节点详情查看
    showInfo(`查看节点 ${nodeId} 的详情功能正在开发中`);
}

// 控制节点
function controlNode(nodeId) {
    // TODO: 实现节点控制
    showInfo(`控制节点 ${nodeId} 的功能正在开发中`);
}

// 删除节点
async function deleteNode(nodeId) {
    if (!confirm('确定要删除这个节点吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/nodes/${nodeId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        await handleApiResponse(response);
        
        showSuccess('节点删除成功');
        loadNodesData();
        
    } catch (error) {
        console.error('Delete node failed:', error);
        showError('删除节点失败: ' + error.message);
    }
}

// 导出节点数据
function exportNodes() {
    showInfo('导出功能正在开发中');
}

// 批量操作
function batchOperation() {
    if (selectedNodes.size === 0) {
        showError('请先选择要操作的节点');
        return;
    }
    
    showInfo(`批量操作功能正在开发中，已选择 ${selectedNodes.size} 个节点`);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}


/* === templates.js === */
// 模板管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载模板数据
    loadTemplatesData();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadTemplatesData, 60000); // 60秒刷新一次
});

// 全局变量
let currentPage = 1;
let pageSize = 12;
let totalPages = 1;
let selectedTemplates = new Set();
let currentViewMode = 'grid'; // grid 或 list

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载模板数据
async function loadTemplatesData() {
    try {
        // 加载统计数据
        await loadTemplateStatistics();
        
        // 加载模板列表
        await loadTemplatesList();
        
    } catch (error) {
        console.error('Load templates data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载模板统计
async function loadTemplateStatistics() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const overview = data.data;
        
        // 更新统计数据
        document.getElementById('total-templates').textContent = overview.templates.total || 0;
        document.getElementById('active-templates').textContent = overview.templates.active || 0;
        document.getElementById('total-deployments').textContent = overview.deployments.total || 0;
        document.getElementById('template-types').textContent = overview.templates.types || 0;
        
    } catch (error) {
        console.error('Load template statistics failed:', error);
        // 显示默认值
        document.getElementById('total-templates').textContent = '0';
        document.getElementById('active-templates').textContent = '0';
        document.getElementById('total-deployments').textContent = '0';
        document.getElementById('template-types').textContent = '0';
    }
}

// 加载模板列表
async function loadTemplatesList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const typeFilter = document.getElementById('type-filter').value;
        const statusFilter = document.getElementById('status-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (typeFilter) params.append('type', typeFilter);
        if (statusFilter) params.append('status', statusFilter);
        
        const response = await fetch(`/api/v1/templates?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const templates = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新模板显示
        if (currentViewMode === 'grid') {
            updateTemplatesGrid(templates);
        } else {
            updateTemplatesTable(templates);
        }
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load templates list failed:', error);
        if (currentViewMode === 'grid') {
            updateTemplatesGrid([]);
        } else {
            updateTemplatesTable([]);
        }
    }
}

// 更新模板网格视图
function updateTemplatesGrid(templates) {
    const grid = document.getElementById('templates-grid');
    
    if (templates.length === 0) {
        grid.innerHTML = `
            <div class="col-12 text-center text-muted py-5">
                <i class="bi bi-inbox display-1 mb-3"></i>
                <h5>暂无模板数据</h5>
                <p>点击"创建模板"按钮添加第一个模板</p>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = templates.map(template => {
        const statusBadge = getStatusBadge(template.status);
        const typeBadge = getTypeBadge(template.type);
        const deploymentCount = template.deployment_count || 0;
        
        return `
            <div class="col-xl-3 col-lg-4 col-md-6 mb-4">
                <div class="card h-100 shadow-sm template-card" data-template-id="${template.id}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            ${typeBadge}
                            <span class="ms-2 fw-bold">${template.name}</span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="viewTemplate('${template.id}')">
                                    <i class="bi bi-eye me-2"></i>查看详情
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="deployTemplate('${template.id}')">
                                    <i class="bi bi-play-circle me-2"></i>部署模板
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="editTemplate('${template.id}')">
                                    <i class="bi bi-pencil me-2"></i>编辑模板
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteTemplate('${template.id}')">
                                    <i class="bi bi-trash me-2"></i>删除模板
                                </a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            ${statusBadge}
                            <span class="badge bg-secondary ms-1">v${template.version}</span>
                        </div>
                        <p class="card-text text-muted small">${template.description || '暂无描述'}</p>
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="text-primary fw-bold">${deploymentCount}</div>
                                <div class="small text-muted">部署次数</div>
                            </div>
                            <div class="col-6">
                                <div class="text-info fw-bold">${formatTime(template.created_at)}</div>
                                <div class="small text-muted">创建时间</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-outline-primary btn-sm" onclick="viewTemplate('${template.id}')">
                                <i class="bi bi-eye me-1"></i>查看
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="deployTemplate('${template.id}')">
                                <i class="bi bi-play-circle me-1"></i>部署
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// 更新模板表格视图
function updateTemplatesTable(templates) {
    const tbody = document.querySelector('#templates-table tbody');
    
    if (templates.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无模板数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = templates.map(template => {
        const statusBadge = getStatusBadge(template.status);
        const typeBadge = getTypeBadge(template.type);
        const deploymentCount = template.deployment_count || 0;
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="template-checkbox" value="${template.id}" 
                           onchange="toggleTemplateSelection('${template.id}')">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-layers me-2 text-primary"></i>
                        ${template.name}
                    </div>
                </td>
                <td>${typeBadge}</td>
                <td><span class="badge bg-secondary">v${template.version}</span></td>
                <td>${statusBadge}</td>
                <td><span class="badge bg-info">${deploymentCount}</span></td>
                <td>${formatTime(template.created_at)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewTemplate('${template.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="deployTemplate('${template.id}')" 
                                title="部署模板">
                            <i class="bi bi-play-circle"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTemplate('${template.id}')" 
                                title="删除模板">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'active': '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>活跃</span>',
        'inactive': '<span class="badge bg-secondary"><i class="bi bi-pause-circle me-1"></i>非活跃</span>',
        'processing': '<span class="badge bg-warning"><i class="bi bi-hourglass-split me-1"></i>处理中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取类型徽章
function getTypeBadge(type) {
    const badges = {
        'ssh': '<span class="badge bg-primary"><i class="bi bi-terminal me-1"></i>SSH</span>',
        'web': '<span class="badge bg-success"><i class="bi bi-globe me-1"></i>Web</span>',
        'ftp': '<span class="badge bg-info"><i class="bi bi-folder me-1"></i>FTP</span>',
        'telnet': '<span class="badge bg-warning"><i class="bi bi-terminal me-1"></i>Telnet</span>',
        'custom': '<span class="badge bg-secondary"><i class="bi bi-gear me-1"></i>自定义</span>'
    };
    return badges[type] || '<span class="badge bg-secondary">未知</span>';
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';
    
    try {
        const time = new Date(timeStr);
        return time.toLocaleDateString();
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }

    currentPage = page;
    loadTemplatesList();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadTemplatesList();
            }, 500);
        });
    }

    // 类型过滤
    const typeFilter = document.getElementById('type-filter');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            currentPage = 1;
            loadTemplatesList();
        });
    }

    // 状态过滤
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1;
            loadTemplatesList();
        });
    }

    // 视图模式切换
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');

    if (gridViewBtn && listViewBtn) {
        gridViewBtn.addEventListener('change', function() {
            if (this.checked) {
                switchViewMode('grid');
            }
        });

        listViewBtn.addEventListener('change', function() {
            if (this.checked) {
                switchViewMode('list');
            }
        });
    }

    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换视图模式
function switchViewMode(mode) {
    currentViewMode = mode;
    const gridView = document.getElementById('templates-grid');
    const listView = document.getElementById('templates-list');

    if (mode === 'grid') {
        gridView.classList.remove('d-none');
        listView.classList.add('d-none');
        pageSize = 12;
    } else {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
        pageSize = 10;
    }

    currentPage = 1;
    loadTemplatesList();
}

// 切换模板选择
function toggleTemplateSelection(templateId) {
    if (selectedTemplates.has(templateId)) {
        selectedTemplates.delete(templateId);
    } else {
        selectedTemplates.add(templateId);
    }

    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.template-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedTemplates.add(checkbox.value);
        } else {
            selectedTemplates.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.template-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');

    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新模板
function refreshTemplates() {
    const refreshBtn = document.querySelector('button[onclick="refreshTemplates()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;

    loadTemplatesData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 添加模板
async function addTemplate() {
    const form = document.getElementById('add-template-form');

    const templateData = {
        name: document.getElementById('template-name').value,
        type: document.getElementById('template-type').value,
        image_name: document.getElementById('template-image').value.split(':')[0] || '',
        image_tag: document.getElementById('template-image').value.split(':')[1] || 'latest',
        version: document.getElementById('template-version').value,
        description: document.getElementById('template-description').value,
        config: document.getElementById('template-config').value
    };

    // 验证表单
    if (!templateData.name || !templateData.type || !templateData.image_name || !templateData.version) {
        showError('请填写所有必填字段');
        return;
    }

    // 验证JSON配置
    if (templateData.config) {
        try {
            JSON.parse(templateData.config);
        } catch (error) {
            showError('配置JSON格式无效');
            return;
        }
    }

    try {
        const response = await fetch('/api/v1/templates', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(templateData)
        });

        const data = await handleApiResponse(response);

        showSuccess('模板创建成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addTemplateModal'));
        modal.hide();

        // 重置表单
        form.reset();

        // 刷新列表
        loadTemplatesData();

    } catch (error) {
        console.error('Add template failed:', error);
        showError('创建模板失败: ' + error.message);
    }
}

// 查看模板详情
function viewTemplate(templateId) {
    showInfo(`查看模板 ${templateId} 的详情功能正在开发中`);
}

// 部署模板
function deployTemplate(templateId) {
    showInfo(`部署模板 ${templateId} 的功能正在开发中`);
}

// 编辑模板
function editTemplate(templateId) {
    showInfo(`编辑模板 ${templateId} 的功能正在开发中`);
}

// 删除模板
async function deleteTemplate(templateId) {
    if (!confirm('确定要删除这个模板吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/templates/${templateId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('模板删除成功');
        loadTemplatesData();

    } catch (error) {
        console.error('Delete template failed:', error);
        showError('删除模板失败: ' + error.message);
    }
}

// 导出模板数据
function exportTemplates() {
    showInfo('导出功能正在开发中');
}

// 批量操作
function batchOperation() {
    if (selectedTemplates.size === 0) {
        showError('请先选择要操作的模板');
        return;
    }

    showInfo(`批量操作功能正在开发中，已选择 ${selectedTemplates.size} 个模板`);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }

    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}


/* === deployments.js === */
// 部署管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载部署数据
    loadDeploymentsData();
    
    // 加载节点和模板选项
    loadSelectOptions();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadDeploymentsData, 30000); // 30秒刷新一次
});

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let selectedDeployments = new Set();

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载部署数据
async function loadDeploymentsData() {
    try {
        // 加载统计数据
        await loadDeploymentStatistics();
        
        // 加载部署列表
        await loadDeploymentsList();
        
    } catch (error) {
        console.error('Load deployments data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载部署统计
async function loadDeploymentStatistics() {
    try {
        const response = await fetch('/api/v1/deployments/statistics', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const stats = data.data;
        
        // 更新统计数据
        document.getElementById('total-deployments').textContent = stats.total || 0;
        document.getElementById('running-deployments').textContent = stats.running || 0;
        document.getElementById('stopped-deployments').textContent = stats.stopped || 0;
        document.getElementById('failed-deployments').textContent = stats.failed || 0;
        
    } catch (error) {
        console.error('Load deployment statistics failed:', error);
        // 显示默认值
        document.getElementById('total-deployments').textContent = '0';
        document.getElementById('running-deployments').textContent = '0';
        document.getElementById('stopped-deployments').textContent = '0';
        document.getElementById('failed-deployments').textContent = '0';
    }
}

// 加载部署列表
async function loadDeploymentsList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const statusFilter = document.getElementById('status-filter').value;
        const nodeFilter = document.getElementById('node-filter').value;
        const templateFilter = document.getElementById('template-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (statusFilter) params.append('status', statusFilter);
        if (nodeFilter) params.append('node_id', nodeFilter);
        if (templateFilter) params.append('template_id', templateFilter);
        
        const response = await fetch(`/api/v1/deployments?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const deployments = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新部署表格
        updateDeploymentsTable(deployments);
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load deployments list failed:', error);
        updateDeploymentsTable([]);
    }
}

// 更新部署表格
function updateDeploymentsTable(deployments) {
    const tbody = document.querySelector('#deployments-table tbody');
    
    if (deployments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无部署数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = deployments.map(deployment => {
        const statusBadge = getStatusBadge(deployment.status);
        const containerID = deployment.container_id ? 
            `<code>${deployment.container_id.substring(0, 12)}</code>` : 
            '<span class="text-muted">-</span>';
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="deployment-checkbox" value="${deployment.id}" 
                           onchange="toggleDeploymentSelection('${deployment.id}')">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-play-circle me-2 text-primary"></i>
                        ${deployment.name}
                    </div>
                </td>
                <td>
                    <span class="badge bg-secondary">${deployment.node_id}</span>
                </td>
                <td>
                    <span class="badge bg-info">模板 ${deployment.template_id}</span>
                </td>
                <td>${statusBadge}</td>
                <td>${containerID}</td>
                <td>${formatTime(deployment.created_at)}</td>
                <td>${formatTime(deployment.updated_at)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewDeployment('${deployment.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${getActionButtons(deployment)}
                        <button class="btn btn-outline-danger" onclick="deleteDeployment('${deployment.id}')" 
                                title="删除部署">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'running': '<span class="badge bg-success"><i class="bi bi-play-circle me-1"></i>运行中</span>',
        'stopped': '<span class="badge bg-warning"><i class="bi bi-pause-circle me-1"></i>已停止</span>',
        'failed': '<span class="badge bg-danger"><i class="bi bi-x-circle me-1"></i>失败</span>',
        'pending': '<span class="badge bg-info"><i class="bi bi-hourglass-split me-1"></i>等待中</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取操作按钮
function getActionButtons(deployment) {
    if (deployment.status === 'running') {
        return `
            <button class="btn btn-outline-warning" onclick="stopDeployment('${deployment.id}')" 
                    title="停止服务">
                <i class="bi bi-pause"></i>
            </button>
            <button class="btn btn-outline-info" onclick="restartDeployment('${deployment.id}')" 
                    title="重启服务">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        `;
    } else if (deployment.status === 'stopped') {
        return `
            <button class="btn btn-outline-success" onclick="startDeployment('${deployment.id}')" 
                    title="启动服务">
                <i class="bi bi-play"></i>
            </button>
        `;
    } else {
        return `
            <button class="btn btn-outline-secondary" disabled title="无可用操作">
                <i class="bi bi-dash"></i>
            </button>
        `;
    }
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '未知';
    
    try {
        const time = new Date(timeStr);
        return time.toLocaleString();
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }
    
    currentPage = page;
    loadDeploymentsList();
}

// 加载选择框选项
async function loadSelectOptions() {
    try {
        // 加载节点选项
        const nodesResponse = await fetch('/api/v1/nodes', {
            headers: getAuthHeaders()
        });
        const nodesData = await handleApiResponse(nodesResponse);
        const nodes = nodesData.data || [];
        
        // 更新节点选择框
        const nodeSelects = ['node-filter', 'deployment-node'];
        nodeSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                const currentValue = select.value;
                const options = nodes.map(node => 
                    `<option value="${node.id}">${node.name} (${node.ip})</option>`
                ).join('');
                
                if (selectId === 'node-filter') {
                    select.innerHTML = '<option value="">所有节点</option>' + options;
                } else {
                    select.innerHTML = '<option value="">选择节点</option>' + options;
                }
                
                select.value = currentValue;
            }
        });
        
        // 加载模板选项
        const templatesResponse = await fetch('/api/v1/templates', {
            headers: getAuthHeaders()
        });
        const templatesData = await handleApiResponse(templatesResponse);
        const templates = templatesData.data || [];
        
        // 更新模板选择框
        const templateSelects = ['template-filter', 'deployment-template'];
        templateSelects.forEach(selectId => {
            const select = document.getElementById(selectId);
            if (select) {
                const currentValue = select.value;
                const options = templates.map(template => 
                    `<option value="${template.id}">${template.name} (${template.type})</option>`
                ).join('');
                
                if (selectId === 'template-filter') {
                    select.innerHTML = '<option value="">所有模板</option>' + options;
                } else {
                    select.innerHTML = '<option value="">选择模板</option>' + options;
                }
                
                select.value = currentValue;
            }
        });
        
    } catch (error) {
        console.error('Load select options failed:', error);
    }
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadDeploymentsList();
            }, 500);
        });
    }

    // 过滤器
    ['status-filter', 'node-filter', 'template-filter'].forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', function() {
                currentPage = 1;
                loadDeploymentsList();
            });
        }
    });

    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换部署选择
function toggleDeploymentSelection(deploymentId) {
    if (selectedDeployments.has(deploymentId)) {
        selectedDeployments.delete(deploymentId);
    } else {
        selectedDeployments.add(deploymentId);
    }

    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.deployment-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedDeployments.add(checkbox.value);
        } else {
            selectedDeployments.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.deployment-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');

    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新部署
function refreshDeployments() {
    const refreshBtn = document.querySelector('button[onclick="refreshDeployments()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;

    loadDeploymentsData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 创建部署
async function createDeployment() {
    const form = document.getElementById('create-deployment-form');

    const deploymentData = {
        name: document.getElementById('deployment-name').value,
        node_id: document.getElementById('deployment-node').value,
        template_id: parseInt(document.getElementById('deployment-template').value),
        ports: document.getElementById('deployment-ports').value.split(',').filter(p => p.trim()),
        environment: {},
        volumes: document.getElementById('deployment-volumes').value.split(',').filter(v => v.trim())
    };

    // 解析环境变量
    const envText = document.getElementById('deployment-env').value.trim();
    if (envText) {
        try {
            deploymentData.environment = JSON.parse(envText);
        } catch (error) {
            showError('环境变量JSON格式无效');
            return;
        }
    }

    // 验证表单
    if (!deploymentData.name || !deploymentData.node_id || !deploymentData.template_id) {
        showError('请填写所有必填字段');
        return;
    }

    try {
        const response = await fetch('/api/v1/deployments', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(deploymentData)
        });

        const data = await handleApiResponse(response);

        showSuccess('部署创建成功');

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createDeploymentModal'));
        modal.hide();

        // 重置表单
        form.reset();

        // 刷新列表
        loadDeploymentsData();

    } catch (error) {
        console.error('Create deployment failed:', error);
        showError('创建部署失败: ' + error.message);
    }
}

// 查看部署详情
function viewDeployment(deploymentId) {
    showInfo(`查看部署 ${deploymentId} 的详情功能正在开发中`);
}

// 启动部署
async function startDeployment(deploymentId) {
    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/start`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署启动命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Start deployment failed:', error);
        showError('启动部署失败: ' + error.message);
    }
}

// 停止部署
async function stopDeployment(deploymentId) {
    if (!confirm('确定要停止这个部署吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/stop`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署停止命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Stop deployment failed:', error);
        showError('停止部署失败: ' + error.message);
    }
}

// 重启部署
async function restartDeployment(deploymentId) {
    if (!confirm('确定要重启这个部署吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}/restart`, {
            method: 'POST',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署重启命令已发送');
        loadDeploymentsData();

    } catch (error) {
        console.error('Restart deployment failed:', error);
        showError('重启部署失败: ' + error.message);
    }
}

// 删除部署
async function deleteDeployment(deploymentId) {
    if (!confirm('确定要删除这个部署吗？此操作不可恢复。')) {
        return;
    }

    try {
        const response = await fetch(`/api/v1/deployments/${deploymentId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });

        await handleApiResponse(response);

        showSuccess('部署删除成功');
        loadDeploymentsData();

    } catch (error) {
        console.error('Delete deployment failed:', error);
        showError('删除部署失败: ' + error.message);
    }
}

// 批量操作
function batchStart() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要启动的部署');
        return;
    }

    showInfo(`批量启动功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

function batchStop() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要停止的部署');
        return;
    }

    showInfo(`批量停止功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

function batchDelete() {
    if (selectedDeployments.size === 0) {
        showError('请先选择要删除的部署');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedDeployments.size} 个部署吗？此操作不可恢复。`)) {
        return;
    }

    showInfo(`批量删除功能正在开发中，已选择 ${selectedDeployments.size} 个部署`);
}

// 导出部署数据
function exportDeployments() {
    showInfo('导出功能正在开发中');
}

// 刷新所有状态
function refreshAll() {
    showInfo('刷新所有状态功能正在开发中');
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }

    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}


/* === intelligence.js === */
// 情报数据页面JavaScript

// 全局变量
let attackTypesChart = null;
let hourlyAttacksChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查认证状态
    checkAuth();
    
    // 初始化页面
    initializePage();
    
    // 绑定事件
    bindEvents();
});

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
    
    // 设置用户信息
    const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
    if (userInfo.username) {
        document.getElementById('user-name').textContent = userInfo.username;
    }
}

// 初始化页面
function initializePage() {
    // 检查DecoyWatch服务健康状态
    checkDecoyWatchHealth();

    // 加载统计数据
    loadAttackStatistics();

    // 加载IP统计数据
    loadIPStatistics();

    // 初始化图表
    initializeCharts();

    // 初始化分析页面
    initializeAnalysisPage();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// 检查DecoyWatch健康状态
async function checkDecoyWatchHealth() {
    const statusElement = document.getElementById('service-status');
    
    try {
        const response = await fetch('/api/v1/intelligence/health', {
            headers: getAuthHeaders()
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                statusElement.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-check-circle-fill text-success me-2"></i>
                        <span class="text-success">DecoyWatch服务正常</span>
                        <small class="text-muted ms-2">(检查时间: ${new Date().toLocaleTimeString()})</small>
                    </div>
                `;
            } else {
                throw new Error(data.message);
            }
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.error('Health check failed:', error);
        statusElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi bi-exclamation-triangle-fill text-warning me-2"></i>
                <span class="text-warning">DecoyWatch服务异常</span>
                <small class="text-muted ms-2">${error.message}</small>
            </div>
        `;
    }
}

// 加载攻击统计数据
async function loadAttackStatistics() {
    try {
        const response = await fetch('/api/v1/intelligence/statistics', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        // 更新统计卡片
        document.getElementById('total-attacks').textContent = formatNumber(data.total_attacks || 0);
        document.getElementById('today-attacks').textContent = formatNumber(data.today_attacks || 0);
        document.getElementById('unique-ips').textContent = formatNumber(data.unique_ips || 0);
        
        // 更新图表数据
        updateAttackTypesChart(data.top_attack_types || []);
        updateHourlyAttacksChart(data.hourly_stats || []);
        updateTopPortsTable(data.top_target_ports || []);
        
    } catch (error) {
        console.error('Load attack statistics failed:', error);
        showError('加载攻击统计数据失败: ' + error.message);
    }
}

// 加载IP统计数据
async function loadIPStatistics() {
    try {
        const response = await fetch('/api/v1/intelligence/ip-statistics?limit=10', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        
        updateTopIPsTable(data || []);
        
    } catch (error) {
        console.error('Load IP statistics failed:', error);
        showError('加载IP统计数据失败: ' + error.message);
    }
}

// 初始化图表
function initializeCharts() {
    // 攻击类型分布饼图
    const attackTypesCtx = document.getElementById('attack-types-chart').getContext('2d');
    attackTypesChart = new Chart(attackTypesCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // 24小时攻击趋势线图
    const hourlyAttacksCtx = document.getElementById('hourly-attacks-chart').getContext('2d');
    hourlyAttacksChart = new Chart(hourlyAttacksCtx, {
        type: 'line',
        data: {
            labels: Array.from({length: 24}, (_, i) => `${i}:00`),
            datasets: [{
                label: '攻击次数',
                data: new Array(24).fill(0),
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 更新攻击类型图表
function updateAttackTypesChart(attackTypes) {
    if (!attackTypesChart) return;
    
    const labels = attackTypes.map(item => item.attack_type || item.type);
    const data = attackTypes.map(item => item.count);
    
    attackTypesChart.data.labels = labels;
    attackTypesChart.data.datasets[0].data = data;
    attackTypesChart.update();
}

// 更新24小时攻击趋势图表
function updateHourlyAttacksChart(hourlyStats) {
    if (!hourlyAttacksChart) return;
    
    const data = new Array(24).fill(0);
    hourlyStats.forEach(stat => {
        if (stat.hour >= 0 && stat.hour < 24) {
            data[stat.hour] = stat.count;
        }
    });
    
    hourlyAttacksChart.data.datasets[0].data = data;
    hourlyAttacksChart.update();
}

// 更新Top IP表格
function updateTopIPsTable(ipStats) {
    const tbody = document.getElementById('top-ips-table');
    
    if (ipStats.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = ipStats.map(ip => `
        <tr>
            <td><code>${ip.source_ip || ip.ip}</code></td>
            <td><span class="badge bg-danger">${formatNumber(ip.count)}</span></td>
            <td>${ip.country || '-'}</td>
        </tr>
    `).join('');
}

// 更新Top端口表格
function updateTopPortsTable(portStats) {
    const tbody = document.getElementById('top-ports-table');
    
    if (portStats.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = portStats.map(port => `
        <tr>
            <td><code>${port.target_port || port.port}</code></td>
            <td><span class="badge bg-warning">${formatNumber(port.count)}</span></td>
            <td>${getPortService(port.target_port || port.port)}</td>
        </tr>
    `).join('');
}

// 获取端口对应的服务名
function getPortService(port) {
    const services = {
        22: 'SSH',
        23: 'Telnet',
        25: 'SMTP',
        53: 'DNS',
        80: 'HTTP',
        110: 'POP3',
        143: 'IMAP',
        443: 'HTTPS',
        993: 'IMAPS',
        995: 'POP3S',
        3389: 'RDP',
        5432: 'PostgreSQL',
        3306: 'MySQL'
    };
    return services[port] || 'Unknown';
}

// 刷新数据
function refreshData() {
    const refreshBtn = document.querySelector('button[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    Promise.all([
        checkDecoyWatchHealth(),
        loadAttackStatistics(),
        loadIPStatistics()
    ]).finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data.data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}

// ==================== 攻击数据分析功能 ====================

// 加载攻击趋势分析
async function loadAttackTrendAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/trends?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新趋势分析显示
        updateTrendAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载攻击趋势分析失败:', error);
        showAlert('加载攻击趋势分析失败: ' + error.message, 'error');
        return null;
    }
}

// 加载IP地理分布分析
async function loadIPGeographicAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/geographic?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新地理分析显示
        updateGeographicAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载IP地理分布分析失败:', error);
        showAlert('加载IP地理分布分析失败: ' + error.message, 'error');
        return null;
    }
}

// 加载攻击类型分析
async function loadAttackTypeAnalysis(timeRange = '24h', nodeId = '') {
    try {
        const params = new URLSearchParams();
        params.append('time_range', timeRange);
        if (nodeId) {
            params.append('node_id', nodeId);
        }

        const response = await fetch(`/api/v1/intelligence/analysis/attack-types?${params}`, {
            headers: getAuthHeaders()
        });

        const data = await handleApiResponse(response);

        // 更新攻击类型分析显示
        updateAttackTypeAnalysisDisplay(data);

        return data;
    } catch (error) {
        console.error('加载攻击类型分析失败:', error);
        showAlert('加载攻击类型分析失败: ' + error.message, 'error');
        return null;
    }
}

// 更新趋势分析显示
function updateTrendAnalysisDisplay(data) {
    // 更新趋势摘要
    const summaryElement = document.getElementById('trend-summary');
    if (summaryElement && data.summary) {
        summaryElement.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>平均每小时攻击</h6>
                        <h4>${formatNumber(data.summary.avg_attacks_per_hour)}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>最高峰值</h6>
                        <h4>${formatNumber(data.summary.max_attacks_per_hour)}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>增长率</h6>
                        <h4 class="${data.growth_rate >= 0 ? 'text-danger' : 'text-success'}">
                            ${data.growth_rate >= 0 ? '+' : ''}${data.growth_rate.toFixed(1)}%
                        </h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>趋势方向</h6>
                        <h4 class="${getTrendDirectionClass(data.summary.trend_direction)}">
                            ${getTrendDirectionText(data.summary.trend_direction)}
                        </h4>
                    </div>
                </div>
            </div>
        `;
    }

    // 更新趋势图表
    if (data.trend_data && data.trend_data.length > 0) {
        updateTrendChart(data.trend_data);
    }

    // 更新高峰时段
    const peakHoursElement = document.getElementById('peak-hours');
    if (peakHoursElement && data.peak_hours) {
        peakHoursElement.innerHTML = data.peak_hours.map(hour =>
            `<span class="badge bg-warning me-1">${hour}:00</span>`
        ).join('');
    }
}

// 更新地理分析显示
function updateGeographicAnalysisDisplay(data) {
    // 更新国家统计
    const countryStatsElement = document.getElementById('country-stats');
    if (countryStatsElement && data.country_stats) {
        const countryHtml = data.country_stats.slice(0, 10).map(country => `
            <tr>
                <td>
                    <span class="badge bg-secondary me-2">${country.country_code}</span>
                    ${country.country}
                </td>
                <td>${formatNumber(country.attack_count)}</td>
                <td>${formatNumber(country.unique_ips)}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: ${country.percentage}%">
                            ${country.percentage.toFixed(1)}%
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');

        countryStatsElement.innerHTML = countryHtml;
    }

    // 更新顶级攻击者IP
    const topAttackersElement = document.getElementById('top-attackers');
    if (topAttackersElement && data.top_attacker_ips) {
        const attackersHtml = data.top_attacker_ips.slice(0, 10).map(attacker => `
            <tr>
                <td>
                    <code>${attacker.ip}</code>
                    <span class="badge bg-${getThreatLevelClass(attacker.threat_level)} ms-2">
                        ${attacker.threat_level.toUpperCase()}
                    </span>
                </td>
                <td>${formatNumber(attacker.attack_count)}</td>
                <td>${attacker.country || 'Unknown'}</td>
                <td>
                    ${attacker.attack_types.map(type =>
                        `<span class="badge bg-secondary me-1">${type}</span>`
                    ).join('')}
                </td>
                <td>${formatDateTime(attacker.last_seen)}</td>
            </tr>
        `).join('');

        topAttackersElement.innerHTML = attackersHtml;
    }
}

// 更新攻击类型分析显示
function updateAttackTypeAnalysisDisplay(data) {
    // 更新威胁评估
    const threatAssessmentElement = document.getElementById('threat-assessment');
    if (threatAssessmentElement && data.threat_assessment) {
        const assessment = data.threat_assessment;
        threatAssessmentElement.innerHTML = `
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>整体威胁级别</h6>
                        <h4 class="${getThreatLevelClass(assessment.overall_threat_level)}">
                            ${assessment.overall_threat_level.toUpperCase()}
                        </h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>高威胁类型</h6>
                        <h4>${assessment.high_threat_types ? assessment.high_threat_types.length : 0}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>新兴威胁</h6>
                        <h4>${assessment.emerging_threats ? assessment.emerging_threats.length : 0}</h4>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <h6>攻击类型总数</h6>
                        <h4>${data.total_attack_types}</h4>
                    </div>
                </div>
            </div>
        `;
    }

    // 更新攻击类型分布
    const typeDistributionElement = document.getElementById('type-distribution');
    if (typeDistributionElement && data.type_distribution) {
        const distributionHtml = data.type_distribution.map(type => `
            <tr>
                <td>
                    <span class="badge bg-${getSeverityClass(type.severity)} me-2">
                        ${type.severity.toUpperCase()}
                    </span>
                    ${type.attack_type}
                </td>
                <td>${formatNumber(type.count)}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar bg-${getSeverityClass(type.severity)}"
                             role="progressbar" style="width: ${type.percentage}%">
                            ${type.percentage.toFixed(1)}%
                        </div>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${type.description}</small>
                </td>
            </tr>
        `).join('');

        typeDistributionElement.innerHTML = distributionHtml;
    }
}

// 辅助函数
function getTrendDirectionClass(direction) {
    switch (direction) {
        case 'increasing': return 'text-danger';
        case 'decreasing': return 'text-success';
        default: return 'text-warning';
    }
}

function getTrendDirectionText(direction) {
    switch (direction) {
        case 'increasing': return '上升';
        case 'decreasing': return '下降';
        default: return '稳定';
    }
}

function getThreatLevelClass(level) {
    switch (level) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'success';
        default: return 'secondary';
    }
}

function getSeverityClass(severity) {
    switch (severity) {
        case 'critical': return 'danger';
        case 'high': return 'warning';
        case 'medium': return 'info';
        case 'low': return 'success';
        default: return 'secondary';
    }
}

// 更新趋势图表
function updateTrendChart(trendData) {
    const ctx = document.getElementById('trend-chart');
    if (!ctx) return;

    // 如果图表已存在，先销毁
    if (window.trendChart) {
        window.trendChart.destroy();
    }

    const labels = trendData.map(point => {
        const date = new Date(point.timestamp);
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    });

    const attackCounts = trendData.map(point => point.attack_count);
    const uniqueIPs = trendData.map(point => point.unique_ips);

    window.trendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '攻击次数',
                data: attackCounts,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1,
                yAxisID: 'y'
            }, {
                label: '唯一IP数',
                data: uniqueIPs,
                borderColor: 'rgb(54, 162, 235)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '攻击次数'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '唯一IP数'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: '攻击趋势分析'
                },
                legend: {
                    display: true
                }
            }
        }
    });
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 初始化分析页面
function initializeAnalysisPage() {
    // 加载所有分析数据
    loadAttackTrendAnalysis();
    loadIPGeographicAnalysis();
    loadAttackTypeAnalysis();

    // 绑定时间范围选择器
    const timeRangeSelectors = document.querySelectorAll('.time-range-selector');
    timeRangeSelectors.forEach(selector => {
        selector.addEventListener('change', function() {
            const timeRange = this.value;
            const nodeId = document.getElementById('node-filter')?.value || '';

            // 重新加载所有分析数据
            loadAttackTrendAnalysis(timeRange, nodeId);
            loadIPGeographicAnalysis(timeRange, nodeId);
            loadAttackTypeAnalysis(timeRange, nodeId);
        });
    });

    // 绑定节点过滤器
    const nodeFilter = document.getElementById('node-filter');
    if (nodeFilter) {
        nodeFilter.addEventListener('change', function() {
            const nodeId = this.value;
            const timeRange = document.querySelector('.time-range-selector')?.value || '24h';

            // 重新加载所有分析数据
            loadAttackTrendAnalysis(timeRange, nodeId);
            loadIPGeographicAnalysis(timeRange, nodeId);
            loadAttackTypeAnalysis(timeRange, nodeId);
        });
    }
}


