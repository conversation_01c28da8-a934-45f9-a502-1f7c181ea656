// 登录页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const loginBtn = document.getElementById('login-btn');
    const loginSpinner = document.getElementById('login-spinner');
    const errorAlert = document.getElementById('error-alert');
    const errorMessage = document.getElementById('error-message');
    const togglePassword = document.getElementById('toggle-password');
    const passwordInput = document.getElementById('password');
    const usernameInput = document.getElementById('username');

    // 密码显示/隐藏切换
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = togglePassword.querySelector('i');
            if (type === 'password') {
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            } else {
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            }
        });
    }

    // 表单验证
    function validateForm() {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!username) {
            showError('请输入用户名');
            usernameInput.focus();
            return false;
        }
        
        if (username.length < 3) {
            showError('用户名至少需要3个字符');
            usernameInput.focus();
            return false;
        }
        
        if (!password) {
            showError('请输入密码');
            passwordInput.focus();
            return false;
        }
        
        if (password.length < 6) {
            showError('密码至少需要6个字符');
            passwordInput.focus();
            return false;
        }
        
        return true;
    }

    // 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorAlert.classList.remove('d-none');
        
        // 3秒后自动隐藏
        setTimeout(() => {
            hideError();
        }, 3000);
    }

    // 隐藏错误信息
    function hideError() {
        errorAlert.classList.add('d-none');
    }

    // 设置加载状态
    function setLoading(loading) {
        if (loading) {
            loginBtn.disabled = true;
            loginSpinner.classList.remove('d-none');
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>登录中...';
        } else {
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
            loginBtn.innerHTML = '登录';
        }
    }

    // 登录请求
    async function login(username, password) {
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // 登录成功
                const token = data.data.token;
                const userInfo = data.data.user_info;
                
                // 保存token到localStorage
                localStorage.setItem('token', token);
                localStorage.setItem('user_info', JSON.stringify(userInfo));
                
                // 检查是否记住我
                const rememberMe = document.getElementById('remember-me').checked;
                if (rememberMe) {
                    localStorage.setItem('remember_me', 'true');
                    localStorage.setItem('username', username);
                } else {
                    localStorage.removeItem('remember_me');
                    localStorage.removeItem('username');
                }
                
                // 显示成功消息
                showSuccess('登录成功，正在跳转...');
                
                // 延迟跳转到仪表板
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
                
            } else {
                // 登录失败
                const errorMsg = data.message || '登录失败，请检查用户名和密码';
                showError(errorMsg);
            }
        } catch (error) {
            console.error('Login error:', error);
            showError('网络错误，请稍后重试');
        }
    }

    // 显示成功信息
    function showSuccess(message) {
        // 隐藏错误信息
        hideError();
        
        // 创建成功提示
        const successAlert = document.createElement('div');
        successAlert.className = 'alert alert-success';
        successAlert.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>
            ${message}
        `;
        
        // 插入到表单前面
        loginForm.parentNode.insertBefore(successAlert, loginForm);
        
        // 3秒后移除
        setTimeout(() => {
            successAlert.remove();
        }, 3000);
    }

    // 表单提交事件
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 隐藏之前的错误信息
            hideError();
            
            // 验证表单
            if (!validateForm()) {
                return;
            }
            
            // 设置加载状态
            setLoading(true);
            
            // 获取表单数据
            const username = usernameInput.value.trim();
            const password = passwordInput.value.trim();
            
            // 执行登录
            await login(username, password);
            
            // 取消加载状态
            setLoading(false);
        });
    }

    // 输入框事件监听
    [usernameInput, passwordInput].forEach(input => {
        if (input) {
            input.addEventListener('input', function() {
                // 输入时隐藏错误信息
                hideError();
                
                // 移除验证样式
                input.classList.remove('is-invalid', 'is-valid');
            });
            
            input.addEventListener('blur', function() {
                // 失焦时验证
                const value = input.value.trim();
                if (value) {
                    if (input === usernameInput && value.length >= 3) {
                        input.classList.add('is-valid');
                        input.classList.remove('is-invalid');
                    } else if (input === passwordInput && value.length >= 6) {
                        input.classList.add('is-valid');
                        input.classList.remove('is-invalid');
                    } else {
                        input.classList.add('is-invalid');
                        input.classList.remove('is-valid');
                    }
                }
            });
        }
    });

    // 页面加载时检查是否记住用户名
    const rememberedUsername = localStorage.getItem('username');
    const rememberMe = localStorage.getItem('remember_me');
    
    if (rememberMe === 'true' && rememberedUsername) {
        usernameInput.value = rememberedUsername;
        document.getElementById('remember-me').checked = true;
        passwordInput.focus();
    } else {
        usernameInput.focus();
    }

    // 检查是否已经登录
    const token = localStorage.getItem('token');
    if (token) {
        // 验证token是否有效
        fetch('/api/v1/auth/profile', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        })
        .then(response => {
            if (response.ok) {
                // token有效，直接跳转到仪表板
                window.location.href = '/dashboard';
            }
        })
        .catch(error => {
            // token无效，清除本地存储
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(e) {
        // ESC键清除错误信息
        if (e.key === 'Escape') {
            hideError();
        }
    });
});
