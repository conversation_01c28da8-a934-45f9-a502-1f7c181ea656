// 节点管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 加载用户信息
    loadUserInfo();
    
    // 加载节点数据
    loadNodesData();
    
    // 绑定事件
    bindEvents();
    
    // 设置定时刷新
    setInterval(loadNodesData, 30000); // 30秒刷新一次
});

// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let selectedNodes = new Set();

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = '/login';
        return;
    }
}

// 加载用户信息
function loadUserInfo() {
    const userInfo = localStorage.getItem('user_info');
    if (userInfo) {
        try {
            const user = JSON.parse(userInfo);
            document.getElementById('username').textContent = user.username || '用户';
        } catch (error) {
            console.error('Parse user info failed:', error);
        }
    }
}

// 加载节点数据
async function loadNodesData() {
    try {
        // 加载统计数据
        await loadNodeStatistics();
        
        // 加载节点列表
        await loadNodesList();
        
    } catch (error) {
        console.error('Load nodes data failed:', error);
        showError('加载数据失败: ' + error.message);
    }
}

// 加载节点统计
async function loadNodeStatistics() {
    try {
        const response = await fetch('/api/v1/dashboard/overview', {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const overview = data.data;
        
        // 更新统计数据
        document.getElementById('total-nodes').textContent = overview.nodes.total || 0;
        document.getElementById('online-nodes').textContent = overview.nodes.online || 0;
        document.getElementById('offline-nodes').textContent = overview.nodes.offline || 0;
        document.getElementById('running-services').textContent = overview.deployments.running || 0;
        
    } catch (error) {
        console.error('Load node statistics failed:', error);
        // 显示默认值
        document.getElementById('total-nodes').textContent = '0';
        document.getElementById('online-nodes').textContent = '0';
        document.getElementById('offline-nodes').textContent = '0';
        document.getElementById('running-services').textContent = '0';
    }
}

// 加载节点列表
async function loadNodesList() {
    try {
        const searchKeyword = document.getElementById('search-input').value;
        const statusFilter = document.getElementById('status-filter').value;
        const regionFilter = document.getElementById('region-filter').value;
        
        const params = new URLSearchParams({
            page: currentPage,
            size: pageSize
        });
        
        if (searchKeyword) params.append('keyword', searchKeyword);
        if (statusFilter) params.append('status', statusFilter);
        if (regionFilter) params.append('region', regionFilter);
        
        const response = await fetch(`/api/v1/nodes?${params}`, {
            headers: getAuthHeaders()
        });
        
        const data = await handleApiResponse(response);
        const nodes = data.data || [];
        const pagination = data.pagination || {};
        
        // 更新节点表格
        updateNodesTable(nodes);
        
        // 更新分页
        updatePagination(pagination);
        
    } catch (error) {
        console.error('Load nodes list failed:', error);
        updateNodesTable([]);
    }
}

// 更新节点表格
function updateNodesTable(nodes) {
    const tbody = document.querySelector('#nodes-table tbody');
    
    if (nodes.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>暂无节点数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = nodes.map(node => {
        const statusBadge = getStatusBadge(node.status);
        const lastHeartbeat = formatTime(node.last_heartbeat_at);
        const runningServices = node.running_services || 0;
        
        return `
            <tr>
                <td>
                    <input type="checkbox" class="node-checkbox" value="${node.id}" 
                           onchange="toggleNodeSelection('${node.id}')">
                </td>
                <td><code>${node.id}</code></td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-hdd-network me-2 text-primary"></i>
                        ${node.name}
                    </div>
                </td>
                <td>${node.ip}</td>
                <td>
                    <span class="badge bg-secondary">${getRegionName(node.region)}</span>
                </td>
                <td>${statusBadge}</td>
                <td>
                    <span class="badge bg-info">${runningServices} 个</span>
                </td>
                <td>${lastHeartbeat}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewNode('${node.id}')" 
                                title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="controlNode('${node.id}')" 
                                title="控制节点">
                            <i class="bi bi-gear"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteNode('${node.id}')" 
                                title="删除节点">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'online': '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>在线</span>',
        'offline': '<span class="badge bg-secondary"><i class="bi bi-x-circle me-1"></i>离线</span>',
        'error': '<span class="badge bg-danger"><i class="bi bi-exclamation-triangle me-1"></i>错误</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 获取区域名称
function getRegionName(region) {
    const regions = {
        'asia': '亚洲',
        'europe': '欧洲',
        'america': '美洲'
    };
    return regions[region] || region;
}

// 格式化时间
function formatTime(timeStr) {
    if (!timeStr) return '从未';
    
    try {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now - time;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前';
        } else {
            return time.toLocaleDateString();
        }
    } catch (error) {
        return '无效时间';
    }
}

// 更新分页
function updatePagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    
    if (!pagination.total || pagination.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    totalPages = pagination.total_pages || 1;
    currentPage = pagination.page || 1;
    
    let paginationHTML = '';
    
    // 上一页
    paginationHTML += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHTML += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    paginationElement.innerHTML = paginationHTML;
}

// 切换页面
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }
    
    currentPage = page;
    loadNodesList();
}

// 绑定事件
function bindEvents() {
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // 搜索输入
    const searchInput = document.getElementById('search-input');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadNodesList();
            }, 500);
        });
    }
    
    // 状态过滤
    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1;
            loadNodesList();
        });
    }
    
    // 区域过滤
    const regionFilter = document.getElementById('region-filter');
    if (regionFilter) {
        regionFilter.addEventListener('change', function() {
            currentPage = 1;
            loadNodesList();
        });
    }
    
    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            toggleSelectAll(this.checked);
        });
    }
}

// 切换节点选择
function toggleNodeSelection(nodeId) {
    if (selectedNodes.has(nodeId)) {
        selectedNodes.delete(nodeId);
    } else {
        selectedNodes.add(nodeId);
    }
    
    updateSelectAllState();
}

// 切换全选
function toggleSelectAll(checked) {
    const checkboxes = document.querySelectorAll('.node-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = checked;
        if (checked) {
            selectedNodes.add(checkbox.value);
        } else {
            selectedNodes.delete(checkbox.value);
        }
    });
}

// 更新全选状态
function updateSelectAllState() {
    const checkboxes = document.querySelectorAll('.node-checkbox');
    const selectAllCheckbox = document.getElementById('select-all');
    
    if (checkboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        return;
    }
    
    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    
    if (checkedCount === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCount === checkboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

// 刷新节点
function refreshNodes() {
    const refreshBtn = document.querySelector('button[onclick="refreshNodes()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>刷新中...';
    refreshBtn.disabled = true;
    
    loadNodesData().finally(() => {
        setTimeout(() => {
            refreshBtn.innerHTML = originalText;
            refreshBtn.disabled = false;
        }, 1000);
    });
}

// 添加节点
async function addNode() {
    const form = document.getElementById('add-node-form');
    const formData = new FormData(form);
    
    const nodeData = {
        name: document.getElementById('node-name').value,
        ip: document.getElementById('node-ip').value,
        region: document.getElementById('node-region').value,
        description: document.getElementById('node-description').value
    };
    
    // 验证表单
    if (!nodeData.name || !nodeData.ip || !nodeData.region) {
        showError('请填写所有必填字段');
        return;
    }
    
    try {
        const response = await fetch('/api/v1/nodes', {
            method: 'POST',
            headers: getAuthHeaders(),
            body: JSON.stringify(nodeData)
        });
        
        const data = await handleApiResponse(response);
        
        showSuccess('节点添加成功');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addNodeModal'));
        modal.hide();
        
        // 重置表单
        form.reset();
        
        // 刷新列表
        loadNodesData();
        
    } catch (error) {
        console.error('Add node failed:', error);
        showError('添加节点失败: ' + error.message);
    }
}

// 查看节点详情
function viewNode(nodeId) {
    // TODO: 实现节点详情查看
    showInfo(`查看节点 ${nodeId} 的详情功能正在开发中`);
}

// 控制节点
function controlNode(nodeId) {
    // TODO: 实现节点控制
    showInfo(`控制节点 ${nodeId} 的功能正在开发中`);
}

// 删除节点
async function deleteNode(nodeId) {
    if (!confirm('确定要删除这个节点吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/nodes/${nodeId}`, {
            method: 'DELETE',
            headers: getAuthHeaders()
        });
        
        await handleApiResponse(response);
        
        showSuccess('节点删除成功');
        loadNodesData();
        
    } catch (error) {
        console.error('Delete node failed:', error);
        showError('删除节点失败: ' + error.message);
    }
}

// 导出节点数据
function exportNodes() {
    showInfo('导出功能正在开发中');
}

// 批量操作
function batchOperation() {
    if (selectedNodes.size === 0) {
        showError('请先选择要操作的节点');
        return;
    }
    
    showInfo(`批量操作功能正在开发中，已选择 ${selectedNodes.size} 个节点`);
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        window.location.href = '/login';
    }
}

// 工具函数：获取认证头
function getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };
}

// 工具函数：处理API响应
async function handleApiResponse(response) {
    if (!response.ok) {
        if (response.status === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('user_info');
            window.location.href = '/login';
            return;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    if (!data.success) {
        throw new Error(data.message || '请求失败');
    }
    
    return data;
}

// 显示消息函数
function showError(message) {
    showAlert(message, 'danger');
}

function showSuccess(message) {
    showAlert(message, 'success');
}

function showInfo(message) {
    showAlert(message, 'info');
}

function showAlert(message, type) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, type === 'success' ? 3000 : 5000);
}
