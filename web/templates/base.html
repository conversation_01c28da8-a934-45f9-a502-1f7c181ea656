<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - 蜜罐管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <!-- Custom CSS -->
    <link href="/static/css/main.css" rel="stylesheet">
    
    {{block "head" .}}{{end}}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-shield-check"></i>
                蜜罐管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                            <i class="bi bi-hdd-rack"></i> 节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                            <i class="bi bi-box"></i> 模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "attacks"}}active{{end}}" href="/attacks">
                            <i class="bi bi-exclamation-triangle"></i> 攻击数据
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "reports"}}active{{end}}" href="/reports">
                            <i class="bi bi-bar-chart"></i> 报表分析
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {{if .User}}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person"></i> {{.User.Username}}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile"><i class="bi bi-gear"></i> 个人设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </li>
                    {{else}}
                    <li class="nav-item">
                        <a class="nav-link" href="/login">
                            <i class="bi bi-box-arrow-in-right"></i> 登录
                        </a>
                    </li>
                    {{end}}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                                <i class="bi bi-house"></i> 概览
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "realtime"}}active{{end}}" href="/realtime">
                                <i class="bi bi-eye"></i> 实时监控
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>节点管理</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                                <i class="bi bi-hdd-rack"></i> 节点列表
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "node-status"}}active{{end}}" href="/nodes/status">
                                <i class="bi bi-heart-pulse"></i> 状态监控
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>服务管理</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                                <i class="bi bi-box"></i> 模板管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "deployments"}}active{{end}}" href="/deployments">
                                <i class="bi bi-rocket-takeoff"></i> 部署管理
                            </a>
                        </li>
                    </ul>

                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>数据分析</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "attacks"}}active{{end}}" href="/attacks">
                                <i class="bi bi-bug"></i> 攻击数据
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "statistics"}}active{{end}}" href="/statistics">
                                <i class="bi bi-graph-up"></i> 统计分析
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "reports"}}active{{end}}" href="/reports">
                                <i class="bi bi-file-earmark-text"></i> 报表生成
                            </a>
                        </li>
                    </ul>

                    {{if and .User (eq .User.Role "administrator")}}
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>系统管理</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "users"}}active{{end}}" href="/users">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "settings"}}active{{end}}" href="/settings">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{if eq .ActivePage "logs"}}active{{end}}" href="/logs">
                                <i class="bi bi-list-ul"></i> 系统日志
                            </a>
                        </li>
                    </ul>
                    {{end}}
                </div>
            </nav>

            <!-- Main content area -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Breadcrumb -->
                {{if .Breadcrumb}}
                <nav aria-label="breadcrumb" class="pt-3">
                    <ol class="breadcrumb">
                        {{range .Breadcrumb}}
                        <li class="breadcrumb-item {{if .Active}}active{{end}}">
                            {{if .URL}}<a href="{{.URL}}">{{.Name}}</a>{{else}}{{.Name}}{{end}}
                        </li>
                        {{end}}
                    </ol>
                </nav>
                {{end}}

                <!-- Page Header -->
                {{if .PageHeader}}
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{{.PageHeader}}</h1>
                    {{if .PageActions}}
                    <div class="btn-toolbar mb-2 mb-md-0">
                        {{range .PageActions}}
                        <a href="{{.URL}}" class="btn {{.Class}}">
                            {{if .Icon}}<i class="{{.Icon}}"></i>{{end}} {{.Text}}
                        </a>
                        {{end}}
                    </div>
                    {{end}}
                </div>
                {{end}}

                <!-- Flash Messages -->
                <div id="flash-messages">
                    {{if .SuccessMessage}}
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> {{.SuccessMessage}}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {{end}}

                    {{if .ErrorMessage}}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-circle"></i> {{.ErrorMessage}}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {{end}}

                    {{if .InfoMessage}}
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="bi bi-info-circle"></i> {{.InfoMessage}}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {{end}}
                </div>

                <!-- Page Content -->
                {{block "content" .}}{{end}}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <span class="text-muted">© 2024 蜜罐管理平台. All rights reserved.</span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="text-muted">Version 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="/static/js/main.js"></script>
    
    {{block "scripts" .}}{{end}}
</body>
</html>
