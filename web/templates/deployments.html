<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署管理 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-shield-check me-2"></i>
                蜜罐管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/nodes">
                            <i class="bi bi-hdd-network me-1"></i>节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/templates">
                            <i class="bi bi-layers me-1"></i>模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/deployments">
                            <i class="bi bi-play-circle me-1"></i>部署管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/intelligence">
                            <i class="bi bi-shield-exclamation me-1"></i>情报数据
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="username">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-0">
                    <i class="bi bi-play-circle me-2"></i>部署管理
                </h2>
                <p class="text-muted">管理蜜罐服务部署</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDeploymentModal">
                    <i class="bi bi-plus-circle me-2"></i>创建部署
                </button>
                <button class="btn btn-outline-secondary" onclick="refreshDeployments()">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    总部署数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-deployments">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    运行中
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-deployments">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    已停止
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="stopped-deployments">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-pause-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    失败
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="failed-deployments">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索部署名称...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="running">运行中</option>
                    <option value="stopped">已停止</option>
                    <option value="failed">失败</option>
                    <option value="pending">等待中</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="node-filter">
                    <option value="">所有节点</option>
                    <!-- 节点选项将通过JavaScript动态加载 -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="template-filter">
                    <option value="">所有模板</option>
                    <!-- 模板选项将通过JavaScript动态加载 -->
                </select>
            </div>
            <div class="col-md-3 text-end">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success btn-sm" onclick="batchStart()" title="批量启动">
                        <i class="bi bi-play"></i>
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="batchStop()" title="批量停止">
                        <i class="bi bi-pause"></i>
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()" title="批量删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 部署列表 -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">部署列表</h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportDeployments()">
                            <i class="bi bi-download me-2"></i>导出数据
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="refreshAll()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新所有状态
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="deployments-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>名称</th>
                                <th>节点</th>
                                <th>模板</th>
                                <th>状态</th>
                                <th>容器ID</th>
                                <th>创建时间</th>
                                <th>最后更新</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="部署分页" class="mt-3">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 创建部署模态框 -->
    <div class="modal fade" id="createDeploymentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>创建部署
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="create-deployment-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deployment-name" class="form-label">部署名称</label>
                                    <input type="text" class="form-control" id="deployment-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="deployment-node" class="form-label">目标节点</label>
                                    <select class="form-select" id="deployment-node" required>
                                        <option value="">选择节点</option>
                                        <!-- 节点选项将通过JavaScript动态加载 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deployment-template" class="form-label">模板</label>
                            <select class="form-select" id="deployment-template" required>
                                <option value="">选择模板</option>
                                <!-- 模板选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deployment-ports" class="form-label">端口映射</label>
                            <input type="text" class="form-control" id="deployment-ports" 
                                   placeholder="例如: 22:2222,80:8080">
                            <div class="form-text">格式: 容器端口:主机端口，多个端口用逗号分隔</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deployment-env" class="form-label">环境变量</label>
                            <textarea class="form-control" id="deployment-env" rows="3" 
                                      placeholder='{"ENV_VAR": "value"}'></textarea>
                            <div class="form-text">JSON格式的环境变量</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="deployment-volumes" class="form-label">数据卷</label>
                            <input type="text" class="form-control" id="deployment-volumes" 
                                   placeholder="例如: /host/path:/container/path">
                            <div class="form-text">格式: 主机路径:容器路径，多个卷用逗号分隔</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="createDeployment()">创建部署</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/js/deployments.js"></script>
</body>
</html>
