<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 蜜罐管理平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 300;
        }
        
        .login-header .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .login-form {
            padding: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .system-info {
            background: #f8f9fa;
            padding: 1rem;
            text-align: center;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="icon">
                <i class="bi bi-shield-check"></i>
            </div>
            <h2>蜜罐管理平台</h2>
            <p class="mb-0">Honeypot Management System</p>
        </div>
        
        <div class="login-form">
            {{if .ErrorMessage}}
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle"></i> {{.ErrorMessage}}
            </div>
            {{end}}
            
            {{if .InfoMessage}}
            <div class="alert alert-info" role="alert">
                <i class="bi bi-info-circle"></i> {{.InfoMessage}}
            </div>
            {{end}}
            
            <form id="loginForm" method="POST" action="/login">
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                    <label for="username">
                        <i class="bi bi-person"></i> 用户名
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                    <label for="password">
                        <i class="bi bi-lock"></i> 密码
                    </label>
                </div>
                
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        记住我
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100">
                    <i class="bi bi-box-arrow-in-right"></i> 登录
                </button>
            </form>
            
            <div class="forgot-password">
                <a href="/forgot-password">忘记密码？</a>
            </div>
        </div>
        
        <div class="system-info">
            <div class="row">
                <div class="col-6">
                    <i class="bi bi-server"></i> 系统状态: <span class="text-success">正常</span>
                </div>
                <div class="col-6">
                    <i class="bi bi-clock"></i> {{.CurrentTime}}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>登录中...';
            submitBtn.disabled = true;
            
            // 发送登录请求
            fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    password: password,
                    remember: remember
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 保存Token
                    localStorage.setItem('token', data.data.token);
                    localStorage.setItem('user', JSON.stringify(data.data.user_info));
                    
                    showAlert('登录成功，正在跳转...', 'success');
                    
                    // 跳转到仪表板
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 1000);
                } else {
                    showAlert(data.message || '登录失败', 'danger');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                showAlert('网络错误，请稍后重试', 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
        
        // 显示提示信息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // 移除现有的提示
            const existingAlerts = document.querySelectorAll('.alert');
            existingAlerts.forEach(alert => alert.remove());
            
            // 插入新提示
            const form = document.getElementById('loginForm');
            form.parentNode.insertBefore(alertDiv, form);
        }
        
        // 检查是否已登录
        if (localStorage.getItem('token')) {
            window.location.href = '/dashboard';
        }
        
        // 页面加载完成后聚焦用户名输入框
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // 回车键快捷登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
