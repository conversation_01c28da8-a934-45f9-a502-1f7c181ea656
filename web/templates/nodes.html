<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点管理 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-shield-check me-2"></i>
                蜜罐管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/nodes">
                            <i class="bi bi-hdd-network me-1"></i>节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/templates">
                            <i class="bi bi-layers me-1"></i>模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/deployments">
                            <i class="bi bi-play-circle me-1"></i>部署管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/intelligence">
                            <i class="bi bi-shield-exclamation me-1"></i>情报数据
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="username">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-0">
                    <i class="bi bi-hdd-network me-2"></i>节点管理
                </h2>
                <p class="text-muted">管理和监控蜜罐节点</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addNodeModal">
                    <i class="bi bi-plus-circle me-2"></i>添加节点
                </button>
                <button class="btn btn-outline-secondary" onclick="refreshNodes()">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    总节点数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-nodes">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-hdd-network fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    在线节点
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-nodes">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    离线节点
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="offline-nodes">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-x-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    运行服务
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-services">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索节点名称、IP地址...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="online">在线</option>
                    <option value="offline">离线</option>
                    <option value="error">错误</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="region-filter">
                    <option value="">所有区域</option>
                    <option value="asia">亚洲</option>
                    <option value="europe">欧洲</option>
                    <option value="america">美洲</option>
                </select>
            </div>
        </div>

        <!-- 节点列表 -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">节点列表</h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportNodes()">
                            <i class="bi bi-download me-2"></i>导出数据
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="batchOperation()">
                            <i class="bi bi-check2-square me-2"></i>批量操作
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="nodes-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>节点ID</th>
                                <th>名称</th>
                                <th>IP地址</th>
                                <th>区域</th>
                                <th>状态</th>
                                <th>运行服务</th>
                                <th>最后心跳</th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="9" class="text-center">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="节点分页" class="mt-3">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 添加节点模态框 -->
    <div class="modal fade" id="addNodeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>添加节点
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="add-node-form">
                        <div class="mb-3">
                            <label for="node-name" class="form-label">节点名称</label>
                            <input type="text" class="form-control" id="node-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="node-ip" class="form-label">IP地址</label>
                            <input type="text" class="form-control" id="node-ip" required>
                        </div>
                        <div class="mb-3">
                            <label for="node-region" class="form-label">区域</label>
                            <select class="form-select" id="node-region" required>
                                <option value="">选择区域</option>
                                <option value="asia">亚洲</option>
                                <option value="europe">欧洲</option>
                                <option value="america">美洲</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="node-description" class="form-label">描述</label>
                            <textarea class="form-control" id="node-description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addNode()">添加节点</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/js/nodes.js"></script>
</body>
</html>
