{{define "content"}}
<div class="realtime-monitor">
    <!-- 连接状态 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-wifi"></i> 连接状态
                    </h5>
                    <div class="connection-indicator">
                        <span id="connection-status" class="badge bg-secondary">
                            <i class="fas fa-circle"></i> 连接中...
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 mb-0" id="connected-nodes">-</div>
                                <div class="text-muted">在线节点</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 mb-0" id="connected-admins">-</div>
                                <div class="text-muted">在线管理员</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="h4 mb-0" id="total-connections">-</div>
                                <div class="text-muted">总连接数</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 实时数据 -->
    <div class="row">
        <!-- 实时攻击 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bug"></i> 实时攻击
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleAutoScroll('attack-log')">
                            <i class="fas fa-pause" id="attack-scroll-icon"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearLog('attack-log')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="attack-log" class="log-container" style="height: 400px; overflow-y: auto;">
                        <div class="text-center p-4 text-muted">
                            <i class="fas fa-eye"></i><br>
                            等待攻击数据...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 节点状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-server"></i> 节点状态
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleAutoScroll('node-log')">
                            <i class="fas fa-pause" id="node-scroll-icon"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearLog('node-log')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="node-log" class="log-container" style="height: 400px; overflow-y: auto;">
                        <div class="text-center p-4 text-muted">
                            <i class="fas fa-heartbeat"></i><br>
                            等待节点状态更新...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统日志 -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> 系统日志
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary" onclick="toggleAutoScroll('system-log')">
                            <i class="fas fa-pause" id="system-scroll-icon"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="clearLog('system-log')">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="exportLogs()">
                            <i class="fas fa-download"></i> 导出
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="system-log" class="log-container" style="height: 300px; overflow-y: auto;">
                        <div class="text-center p-4 text-muted">
                            <i class="fas fa-terminal"></i><br>
                            等待系统日志...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 控制面板 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i> 控制面板
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>节点控制</h6>
                            <div class="mb-3">
                                <select class="form-select" id="target-node">
                                    <option value="">选择目标节点</option>
                                </select>
                            </div>
                            <div class="btn-group w-100 mb-3">
                                <button type="button" class="btn btn-outline-primary" onclick="sendNodeCommand('status')">
                                    <i class="fas fa-info-circle"></i> 状态查询
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="sendNodeCommand('restart')">
                                    <i class="fas fa-redo"></i> 重启服务
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="sendNodeCommand('stop')">
                                    <i class="fas fa-stop"></i> 停止服务
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>系统通知</h6>
                            <div class="mb-3">
                                <input type="text" class="form-control" id="notification-title" placeholder="通知标题">
                            </div>
                            <div class="mb-3">
                                <textarea class="form-control" id="notification-message" rows="3" placeholder="通知内容"></textarea>
                            </div>
                            <div class="btn-group w-100">
                                <button type="button" class="btn btn-outline-info" onclick="sendNotification('info')">
                                    <i class="fas fa-info"></i> 信息
                                </button>
                                <button type="button" class="btn btn-outline-warning" onclick="sendNotification('warning')">
                                    <i class="fas fa-exclamation-triangle"></i> 警告
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="sendNotification('error')">
                                    <i class="fas fa-times-circle"></i> 错误
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script>
let ws = null;
let autoScroll = {
    'attack-log': true,
    'node-log': true,
    'system-log': true
};
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initWebSocket();
    loadNodeList();
});

// 初始化WebSocket连接
function initWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/admin`;
    
    try {
        ws = new WebSocket(wsUrl);
        
        ws.onopen = function(event) {
            console.log('WebSocket connected');
            updateConnectionStatus('connected');
            reconnectAttempts = 0;
            
            // 发送认证消息（如果需要）
            // ws.send(JSON.stringify({type: 'auth', data: {token: localStorage.getItem('token')}}));
        };
        
        ws.onmessage = function(event) {
            try {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            } catch (e) {
                console.error('Failed to parse WebSocket message:', e);
            }
        };
        
        ws.onclose = function(event) {
            console.log('WebSocket disconnected');
            updateConnectionStatus('disconnected');
            
            // 尝试重连
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                setTimeout(() => {
                    console.log(`Attempting to reconnect (${reconnectAttempts}/${maxReconnectAttempts})`);
                    initWebSocket();
                }, 3000 * reconnectAttempts);
            }
        };
        
        ws.onerror = function(error) {
            console.error('WebSocket error:', error);
            updateConnectionStatus('error');
        };
        
    } catch (e) {
        console.error('Failed to create WebSocket connection:', e);
        updateConnectionStatus('error');
    }
}

// 处理WebSocket消息
function handleWebSocketMessage(message) {
    switch (message.type) {
        case 'attack_data':
            handleAttackData(message);
            break;
            
        case 'node_status':
            handleNodeStatus(message);
            break;
            
        case 'log_data':
            handleLogData(message);
            break;
            
        case 'notification':
            handleNotification(message);
            break;
            
        case 'node_response':
            handleNodeResponse(message);
            break;
            
        default:
            console.log('Unknown message type:', message.type);
    }
}

// 处理攻击数据
function handleAttackData(message) {
    const logContainer = document.getElementById('attack-log');
    const logEntry = createLogEntry(message.data, 'attack');
    
    // 移除占位符
    const placeholder = logContainer.querySelector('.text-center');
    if (placeholder) {
        placeholder.remove();
    }
    
    logContainer.appendChild(logEntry);
    
    // 限制日志条数
    const maxEntries = 100;
    while (logContainer.children.length > maxEntries) {
        logContainer.removeChild(logContainer.firstChild);
    }
    
    // 自动滚动
    if (autoScroll['attack-log']) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

// 处理节点状态
function handleNodeStatus(message) {
    const logContainer = document.getElementById('node-log');
    const logEntry = createLogEntry(message.data, 'node');
    
    // 移除占位符
    const placeholder = logContainer.querySelector('.text-center');
    if (placeholder) {
        placeholder.remove();
    }
    
    logContainer.appendChild(logEntry);
    
    // 限制日志条数
    const maxEntries = 50;
    while (logContainer.children.length > maxEntries) {
        logContainer.removeChild(logContainer.firstChild);
    }
    
    // 自动滚动
    if (autoScroll['node-log']) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
    
    // 更新连接统计
    updateConnectionStats();
}

// 处理日志数据
function handleLogData(message) {
    const logContainer = document.getElementById('system-log');
    const logEntry = createLogEntry(message.data, 'system');
    
    // 移除占位符
    const placeholder = logContainer.querySelector('.text-center');
    if (placeholder) {
        placeholder.remove();
    }
    
    logContainer.appendChild(logEntry);
    
    // 限制日志条数
    const maxEntries = 200;
    while (logContainer.children.length > maxEntries) {
        logContainer.removeChild(logContainer.firstChild);
    }
    
    // 自动滚动
    if (autoScroll['system-log']) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

// 处理通知
function handleNotification(message) {
    const data = message.data;
    showAlert(data.message, data.type || 'info');
}

// 处理节点响应
function handleNodeResponse(message) {
    const data = message.data;
    showAlert(`节点 ${message.from} 响应: ${data.message || '命令执行完成'}`, 'success');
}

// 创建日志条目
function createLogEntry(data, type) {
    const entry = document.createElement('div');
    entry.className = 'log-entry p-2 border-bottom';
    
    const timestamp = new Date().toLocaleTimeString();
    let content = '';
    
    switch (type) {
        case 'attack':
            content = `
                <div class="d-flex justify-content-between">
                    <span class="text-danger"><i class="fas fa-bug"></i> 攻击检测</span>
                    <small class="text-muted">${timestamp}</small>
                </div>
                <div class="mt-1">
                    <strong>源IP:</strong> ${data.source_ip || 'Unknown'} |
                    <strong>类型:</strong> ${data.attack_type || 'Unknown'} |
                    <strong>端口:</strong> ${data.target_port || 'Unknown'}
                </div>
            `;
            break;
            
        case 'node':
            const statusColor = data.status === 'online' ? 'success' : 'danger';
            content = `
                <div class="d-flex justify-content-between">
                    <span class="text-${statusColor}"><i class="fas fa-server"></i> 节点状态</span>
                    <small class="text-muted">${timestamp}</small>
                </div>
                <div class="mt-1">
                    <strong>节点:</strong> ${data.node_id || 'Unknown'} |
                    <strong>状态:</strong> <span class="text-${statusColor}">${data.status || 'Unknown'}</span>
                </div>
            `;
            break;
            
        case 'system':
            content = `
                <div class="d-flex justify-content-between">
                    <span class="text-info"><i class="fas fa-terminal"></i> 系统日志</span>
                    <small class="text-muted">${timestamp}</small>
                </div>
                <div class="mt-1">${data.message || 'No message'}</div>
            `;
            break;
    }
    
    entry.innerHTML = content;
    return entry;
}

// 更新连接状态
function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    
    switch (status) {
        case 'connected':
            statusElement.className = 'badge bg-success';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 已连接';
            break;
        case 'disconnected':
            statusElement.className = 'badge bg-danger';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 已断开';
            break;
        case 'error':
            statusElement.className = 'badge bg-warning';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接错误';
            break;
        default:
            statusElement.className = 'badge bg-secondary';
            statusElement.innerHTML = '<i class="fas fa-circle"></i> 连接中...';
    }
}

// 更新连接统计
function updateConnectionStats() {
    fetch('/api/v1/websocket/status', {
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.data.statistics;
            document.getElementById('connected-nodes').textContent = stats.connected_nodes;
            document.getElementById('connected-admins').textContent = stats.connected_admins;
            document.getElementById('total-connections').textContent = stats.total_connections;
        }
    })
    .catch(error => {
        console.error('Failed to update connection stats:', error);
    });
}

// 加载节点列表
function loadNodeList() {
    fetch('/api/v1/nodes', {
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('target-node');
            select.innerHTML = '<option value="">选择目标节点</option>';
            
            data.data.forEach(node => {
                const option = document.createElement('option');
                option.value = node.id;
                option.textContent = `${node.name} (${node.id})`;
                select.appendChild(option);
            });
        }
    })
    .catch(error => {
        console.error('Failed to load node list:', error);
    });
}

// 发送节点命令
function sendNodeCommand(command) {
    const nodeId = document.getElementById('target-node').value;
    if (!nodeId) {
        showAlert('请选择目标节点', 'warning');
        return;
    }
    
    fetch(`/api/v1/nodes/${nodeId}/command`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        body: JSON.stringify({
            command: command,
            params: {}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`命令 "${command}" 已发送到节点 ${nodeId}`, 'success');
        } else {
            showAlert(data.message || '发送命令失败', 'danger');
        }
    })
    .catch(error => {
        console.error('Failed to send node command:', error);
        showAlert('发送命令失败: ' + error.message, 'danger');
    });
}

// 发送通知
function sendNotification(type) {
    const title = document.getElementById('notification-title').value;
    const message = document.getElementById('notification-message').value;
    
    if (!title || !message) {
        showAlert('请填写通知标题和内容', 'warning');
        return;
    }
    
    fetch('/api/v1/websocket/broadcast', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        body: JSON.stringify({
            title: title,
            message: message,
            type: type
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('通知已广播', 'success');
            document.getElementById('notification-title').value = '';
            document.getElementById('notification-message').value = '';
        } else {
            showAlert(data.message || '发送通知失败', 'danger');
        }
    })
    .catch(error => {
        console.error('Failed to send notification:', error);
        showAlert('发送通知失败: ' + error.message, 'danger');
    });
}

// 切换自动滚动
function toggleAutoScroll(logId) {
    autoScroll[logId] = !autoScroll[logId];
    const icon = document.getElementById(logId.replace('-log', '-scroll-icon'));
    
    if (autoScroll[logId]) {
        icon.className = 'fas fa-pause';
    } else {
        icon.className = 'fas fa-play';
    }
}

// 清空日志
function clearLog(logId) {
    const logContainer = document.getElementById(logId);
    logContainer.innerHTML = '<div class="text-center p-4 text-muted"><i class="fas fa-eye"></i><br>日志已清空</div>';
}

// 导出日志
function exportLogs() {
    // 简化实现，实际应该调用后端API
    showAlert('日志导出功能开发中...', 'info');
}

// 定期更新连接统计
setInterval(updateConnectionStats, 30000);
</script>
{{end}}
