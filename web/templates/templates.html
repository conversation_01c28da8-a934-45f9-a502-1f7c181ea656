<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板管理 - 蜜罐管理平台</title>
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-shield-check me-2"></i>
                蜜罐管理平台
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/nodes">
                            <i class="bi bi-hdd-network me-1"></i>节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/templates">
                            <i class="bi bi-layers me-1"></i>模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/deployments">
                            <i class="bi bi-play-circle me-1"></i>部署管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/intelligence">
                            <i class="bi bi-shield-exclamation me-1"></i>情报数据
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="username">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和操作按钮 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="mb-0">
                    <i class="bi bi-layers me-2"></i>模板管理
                </h2>
                <p class="text-muted">管理蜜罐服务模板</p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                    <i class="bi bi-plus-circle me-2"></i>创建模板
                </button>
                <button class="btn btn-outline-secondary" onclick="refreshTemplates()">
                    <i class="bi bi-arrow-clockwise me-2"></i>刷新
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    总模板数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-templates">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-layers fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    活跃模板
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-templates">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    部署次数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-deployments">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-play-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    模板类型
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="template-types">
                                    <span class="spinner-border spinner-border-sm"></span>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-collection fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和过滤 -->
        <div class="row mb-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索模板名称、描述...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="type-filter">
                    <option value="">所有类型</option>
                    <option value="ssh">SSH</option>
                    <option value="web">Web</option>
                    <option value="ftp">FTP</option>
                    <option value="telnet">Telnet</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">非活跃</option>
                    <option value="processing">处理中</option>
                </select>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group" role="group">
                    <input type="radio" class="btn-check" name="view-mode" id="grid-view" checked>
                    <label class="btn btn-outline-secondary" for="grid-view">
                        <i class="bi bi-grid-3x3-gap"></i>
                    </label>
                    
                    <input type="radio" class="btn-check" name="view-mode" id="list-view">
                    <label class="btn btn-outline-secondary" for="list-view">
                        <i class="bi bi-list"></i>
                    </label>
                </div>
            </div>
        </div>

        <!-- 模板网格视图 -->
        <div id="templates-grid" class="row">
            <!-- 模板卡片将通过JavaScript生成 -->
            <div class="col-12 text-center">
                <span class="spinner-border spinner-border-sm me-2"></span>
                加载中...
            </div>
        </div>

        <!-- 模板列表视图 -->
        <div id="templates-list" class="card shadow d-none">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">模板列表</h6>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportTemplates()">
                            <i class="bi bi-download me-2"></i>导出数据
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="batchOperation()">
                            <i class="bi bi-check2-square me-2"></i>批量操作
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" id="templates-table">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="select-all">
                                </th>
                                <th>名称</th>
                                <th>类型</th>
                                <th>版本</th>
                                <th>状态</th>
                                <th>部署次数</th>
                                <th>创建时间</th>
                                <th width="120">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center">
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="模板分页" class="mt-3">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>

    <!-- 创建模板模态框 -->
    <div class="modal fade" id="addTemplateModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle me-2"></i>创建模板
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="add-template-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template-name" class="form-label">模板名称</label>
                                    <input type="text" class="form-control" id="template-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template-type" class="form-label">模板类型</label>
                                    <select class="form-select" id="template-type" required>
                                        <option value="">选择类型</option>
                                        <option value="ssh">SSH蜜罐</option>
                                        <option value="web">Web蜜罐</option>
                                        <option value="ftp">FTP蜜罐</option>
                                        <option value="telnet">Telnet蜜罐</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template-image" class="form-label">Docker镜像</label>
                                    <input type="text" class="form-control" id="template-image" 
                                           placeholder="例如: honeypot/ssh:latest" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="template-version" class="form-label">版本</label>
                                    <input type="text" class="form-control" id="template-version" 
                                           placeholder="例如: 1.0.0" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template-description" class="form-label">描述</label>
                            <textarea class="form-control" id="template-description" rows="3" 
                                      placeholder="描述模板的功能和用途"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="template-config" class="form-label">默认配置 (JSON)</label>
                            <textarea class="form-control" id="template-config" rows="5" 
                                      placeholder='{"port": 22, "banner": "OpenSSH_8.0"}'></textarea>
                            <div class="form-text">请输入有效的JSON格式配置</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addTemplate()">创建模板</button>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/static/js/templates.js"></script>
</body>
</html>
